/****************************************************************/
/*   NAME: iLodging_HEU_Info                                  */
/*   ORGN: HEU                                                 */
/*   FILE: iLodging_HEU_Info.cpp                              */
/*   DESC: ?????????????????                    */
/*   DATE: 2024                                                */
/****************************************************************/

#include <cstdlib>
#include <iostream>
#include "iLodging_HEU_Info.h"
#include "ColorParse.h"
#include "ReleaseInfo.h"

using namespace std;

//----------------------------------------------------------------
// Procedure: showSynopsis

void showSynopsis()
{
  blk("SYNOPSIS:                                                       ");
  blk("------------------------------------                            ");
  blk("  iLodging_HEU ???????????                           ");
  blk("  ???????CAN??????????????????          ");
  blk("                                                                ");
  blk("  ?????                                                    ");
  blk("  - ?????????????????                          ");
  blk("  - ?????????????????                          ");
  blk("  - ???????????????                              ");
  blk("  - ???????????                                      ");
  blk("  - ??????                                                ");
  blk("  - ?????????                                          ");
  blk("  - ??MOOS????                                            ");
  blk("                                                                ");
}

//----------------------------------------------------------------
// Procedure: showHelpAndExit

void showHelpAndExit()
{
  blk("                                                                ");
  blu("=============================================================== ");
  blu("Usage: iLodging_HEU file.moos [OPTIONS]                        ");
  blu("=============================================================== ");
  blk("                                                                ");
  showSynopsis();
  blk("                                                                ");
  blk("Options:                                                        ");
  mag("  --alias","=<ProcessName>                                      ");
  blk("      Launch iLodging_HEU with the given process name          ");
  blk("      rather than iLodging_HEU.                                ");
  mag("  --example, -e                                                 ");
  blk("      Display example MOOS configuration block.                 ");
  mag("  --help, -h                                                    ");
  blk("      Display this help message.                                ");
  mag("  --interface, -i                                               ");
  blk("      Display MOOS publications and subscriptions.              ");
  mag("  --version,-v                                                  ");
  blk("      Display the release version of iLodging_HEU.             ");
  blk("                                                                ");
  blk("Note: If argv[2] does not otherwise match a known option,       ");
  blk("      then it will be interpreted as a run alias. This is       ");
  blk("      to support pAntler launching conventions.                 ");
  blk("                                                                ");
  exit(0);
}

//----------------------------------------------------------------
// Procedure: showExampleConfigAndExit

void showExampleConfigAndExit()
{
  blk("                                                                ");
  blu("=============================================================== ");
  blu("iLodging_HEU Example MOOS Configuration                        ");
  blu("=============================================================== ");
  blk("                                                                ");
  blk("ProcessConfig = iLodging_HEU                                   ");
  blk("{                                                               ");
  blk("  AppTick   = 10                                                ");
  blk("  CommsTick = 10                                                ");
  blk("                                                                ");
  blk("  //------------------------------------------                  ");
  blk("  // ??????                                               ");
  blk("  //------------------------------------------                  ");
  blk("  RecvIP = \"************\"      // ??IP??                  ");
  blk("  RecvPort = 8004                // ????                    ");
  blk("  DestIP = \"************\"      // CAN??IP??              ");
  blk("  DestPort = 4004                // CAN????                ");
  blk("                                                                ");
  blk("  //------------------------------------------                  ");
  blk("  // ??????                                               ");
  blk("  //------------------------------------------                  ");
  blk("  DEFAULT_SPEED = 50.0           // ????(mm/s?rad/s)       ");
  blk("  MAX_ANGLE = 3600.0             // ????(10?)              ");
  blk("  MIN_ANGLE = 0.0                // ????                    ");
  blk("  TIMEOUT_SEC = 5.0              // ??????(?)            ");
  blk("                                                                ");
  blk("                                                                ");
  blk("}                                                               ");
  blk("                                                                ");
  exit(0);
}


//----------------------------------------------------------------
// Procedure: showInterfaceAndExit

void showInterfaceAndExit()
{
  blk("                                                                ");
  blu("=============================================================== ");
  blu("iLodging_HEU INTERFACE                                         ");
  blu("=============================================================== ");
  blk("                                                                ");
  showSynopsis();
  blk("                                                                ");
  blk("SUBSCRIPTIONS:                                                  ");
  blk("------------------------------------                            ");
  blk("  MOTOR_CMD = string                                            ");
  blk("      ???????????                                    ");
  blk("      \"UP\" / \"RISE\" / \"RISING\" - ???????          ");
  blk("      \"DOWN\" / \"FALL\" / \"FALLING\" - ???????       ");
  blk("      \"STOP\" / \"IDLE\" - ????                           ");
  blk("      \"GOTO:angle\" - ?????????                      ");
  blk("                                                                ");
  blk("  MOTOR_TARGET_POSITION = double                                ");
  blk("      ??????????????                              ");
  blk("      ???MIN_ANGLE ? MAX_ANGLE                             ");
  blk("                                                                ");
  blk("  MOTOR_ENABLE = bool                                           ");
  blk("      ?????? (true=??, false=??)                     ");
  blk("                                                                ");
  blk("  MOTOR_CLEAR_ERROR = string                                    ");
  blk("      ?????? (\"true\" ? ???)                        ");
  blk("                                                                ");
  blk("  MOTOR_SET_MODE = string                                       ");
  blk("      ?????? (\"SPEED\" ? \"ANGLE\")                    ");
  blk("                                                                ");
  blk("PUBLICATIONS:                                                   ");
  blk("------------------------------------                            ");
  blk("  MOTOR_STATUS = string                                         ");
  blk("      ?????????????                                ");
  blk("      \"mode=ANGLE,state=IDLE,speed=0,rounds=5,angle=180,      ");
  blk("       total=1980.0[,error=????]\"                         ");
  blk("                                                                ");
  blk("  MOTOR_POSITION = double                                       ");
  blk("      ??????????*360 + ?????                    ");
  blk("                                                                ");
  blk("  MOTOR_ANGLE = double                                          ");
  blk("      ???????0-359??                                  ");
  blk("                                                                ");
  blk("  MOTOR_ROUNDS = double                                         ");
  blk("      ????                                                  ");
  blk("                                                                ");
  blk("  MOTOR_SPEED = double                                          ");
  blk("      ?????mm/s ? rad/s?                                ");
  blk("                                                                ");
  blk("  MOTOR_MOTION_STATE = string                                   ");
  blk("      ???? (\"RISING\" / \"FALLING\" / \"IDLE\")           ");
  blk("                                                                ");
  blk("  MOTOR_CONTROL_MODE = string                                   ");
  blk("      ???? (\"SPEED\" / \"ANGLE\")                         ");
  blk("                                                                ");
  blk("  MOTOR_ERROR = string                                          ");
  blk("      ??????????\"CLEARED\"?                        ");
  blk("                                                                ");
  blk("  MOTOR_ERROR_CODE = double                                     ");
  blk("      ?????                                                ");
  blk("      0=???, 1=??, 2=??, 3=????,                    ");
  blk("      4=?????, 5=??, 6=??????, 7=DRV??          ");
  blk("                                                                ");
  blk("  MOTOR_CONNECTED = string                                      ");
  blk("      ???? (\"true\" / \"false\")                          ");
  blk("                                                                ");
  blk("  MOTOR_HEARTBEAT = double                                      ");
  blk("      ??????MOOSTime?                                   ");
  blk("                                                                ");
  exit(0);
}

//----------------------------------------------------------------
// Procedure: showReleaseInfoAndExit

void showReleaseInfoAndExit()
{
    showReleaseInfo("iLodging_HEU", "gpl");
  exit(0);
}