/************************************************************/
/*    NAME: zhaoqinchao                                  */
/*    ORGN: HEU                                            */
/*    FILE: iLodging_HEU.cpp                              */
/*    DESC: ĺéçľćşć§ĺśçłťçť                                */
/*          ä¸ĺéçľćşçCANćťçşżéäżĄ                          */
/*    DATE: 2024                                           */
/*    VERSION: 1.2.0                                       */
/*                                                          */
/*    MODIFICATION HISTORY:                                 */
/*    v1.2.0 (2024/12/XX) - zhaoqinchao                     */
/*      - ĺŽĺMotorErrorćä¸žďźćˇťĺ çźç ĺ¨éčŻŻăĺšč˝ŚçľĺčżéŤă?*/
/*        DRVéŠąĺ¨éčŻŻç­éčŻŻç                                */
/*      - éćBlueSocketéčŻčżćĽĺč˝ďźćéŤç˝çťčżćĽç¨łĺŽć?    */
/*      - ćščżéčŻŻĺ¤çĺčľćşçŽĄçďźçĄŽäżĺĽćĽĺ­ć­ŁçĄŽĺłé?        */
/*      - ĺşĺä¸ĺéčŻŻçąťĺçéčŻŻç ďźĺ˘ĺźşçłťçťčŻć­č˝ĺ?        */
/*      - äźĺéç˝Žĺć°ĺ¤çďźćŻćéťčŽ¤ĺźĺĺč­Śćşĺś             */
/*      - ĺčŽŽéčŻŻç č§ŁćďźĺŽç°ĺŽćśçľćşćéĺč­Ś              */
/************************************************************/

#include <iterator>
#include <cmath>
#include "MBUtils.h"
#include "iLodging_HEU.h"
using namespace std;
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/ip.h> /* superset of previous */

//---------------------------------------------------------
// ćé ĺ˝ć?- ĺĺ§ĺććĺé?
iLodging_HEU::iLodging_HEU()
{
    m_nIterations = 0;
    m_dTimewarp = 1;
    SetAppFreq(10);   // ĺşç¨ç¨ĺşé˘ç10Hz
    SetCommsFreq(10); // éäżĄé˘ç10Hz

    // ĺĺ§ĺć§ĺść ĺżä˝ĺĺŤä¸şćĺ¨ä¸ĺďźćĺ¨ä¸éďźčŞĺ¨ć§ĺśďźGFć¨Ąĺź
    m_bManualAscend = false;
    m_bManualDescend = false;
    m_bAutoControl = false;
    m_bGFMode = false;
    m_dTargetAngle = 0.0;

    // ĺĺ§ĺććéĺşŚ
    m_dDesiredSpeed = 0.0;

    // ĺĺ§ĺéĺşŚéç˝Žĺć°ďźéťčŽ¤ĺźďź
    m_dHighSpeed = 2500.0;

    // ĺĺ§ĺçľćľéç˝Žĺć°ďźéťčŽ¤ĺźďź
    m_dCurrentThreshold = 2.0; // éťčŽ¤2.0A

    // ĺĺ§ĺĺäźçľćşč§ĺşŚéç˝Žĺć°ďźéťčŽ¤ĺźďź- ä¸Ľć źćç§ĺäźĺčŽŽ0Â°-90Â°čĺ´
    m_dMaxAscendAngle = 90.0;   // ĺäźçľćşćĺ¤§č§ĺşŚďźĺčŽŽéĺś0Â°-90Â°
    m_dMinDescendAngle = 0.0;   // ĺäźçľćşćĺ°č§ĺşŚďźĺčŽŽéĺś0Â°-90Â°

    // ĺĺ§ĺĺäźçľćşĺžŽč°ĺć°
    m_dZeroOffset = 0.0;        // éśä˝ĺžŽč°ĺç§ťéďźéťčŽ¤ć ĺç§?
    // ĺĺéçľćşéťčŽ¤ĺ?    // ĺéç˝Žďźm_dMaxAscendAngle = 3240.0 (9ĺćťč§ĺş? - ĺˇ˛ĺşĺź?    // ĺéç˝Žďźm_dMinDescendAngle = 360.0 (1ĺćťč§ĺş? - ĺˇ˛ĺşĺź?
    // ĺĺ§ĺä¸ťć§çśćä¸şçŠ?    m_sMainframeState = "";

    // ĺĺ§ĺĺŻźčŞĺäťťĺĄçśćä¸şçŠ?    m_bNavigationStarted = false;
    m_sMissionStatus = "";

    // ĺ˝ĺćˇąĺşŚĺĺ§ĺä¸ş0.7ďźćććˇąĺşŚä¸ş0
    m_dCurrentDepth = 0.7;
    m_dDesiredDepth = 1.0;

    // ĺĺ§ĺĺä¸Şçśćć ĺżä˝ĺĺŤä¸şĺĺ°éĄśďźéĺ°ĺşďźĺ¨čżĺ¨ďźĺĄĺ¨ä¸­é?    m_bAtTop = false;
    m_bAtBottom = false;
    m_bInMotion = false;
    m_bStuckInMiddle = false;

    // ĺĺ§ĺçşżç¨ç¸ĺ?    m_RecvThread = 0;
    m_bThreadRunning = false;

    // ĺĺ§ĺĺŻĺ¨ĺťśčżć§ĺ?    // ĺŻĺ¨ĺťśčżä¸?0ç§?    m_dStartupDelay = 60.0;
    // ĺŻĺ¨ćśé´
    m_dStartupTime = 0.0;
    // ĺŻĺ¨ĺťśčżćŻĺŚĺˇ˛ć§čĄ?    m_bStartupDelayExecuted = false;

    // ĺĺ§ĺčŞĺ¨ć§ĺśçśćć ĺżĺĺŤä¸şĺŻźčŞĺŻĺ¨ĺćŻĺŚĺˇ˛ä¸ć˛ďźäťťĺĄçťćĺćŻĺŚĺˇ˛ä¸ĺďźĺşćĽçśććŻĺŚĺˇ˛ä¸ĺ
    m_bHasDescendedAfterNav = false;
    m_bHasAscendedAfterFinish = false;
    m_bHasAscendedInEmergency = false;
    // ä¸ćŹĄçäťťĺĄçść?    m_sLastMissionStatus = "";

    // ĺĺ§ĺGFć¨Ąĺźĺčľˇçśćčˇč¸?    m_bAscendInProgress = false;
    m_dAscendStartTime = 0.0;
    m_dAscendTimeout = 60.0; // 60ç§čść?
    m_dMotorTotalAngle = 0.0;
    m_uMotorRounds = 0;
    m_uMotorDegrees = 0;
    m_uMotorCurrent = 0;
    m_uMotorState = STATE_IDLE; // ĺĺ§ĺçľćşçśćä¸şIDLE
    m_uMotorError = ERROR_NONE; // ĺĺ§ĺçľćşéčŻŻçśćä¸şć éčŻ?    // ĺĺ§ĺCANćĽćčŽĄćść§ĺś
    m_bCanTimingEnabled = false;

    // ĺĺ§ĺĺĄä˝ćŁćľç¸ĺłĺé?    m_bDescendCommandSent = false;
    m_dDescendCommandTime = 0.0;
    m_uLastRounds = 0;
    m_bStuckDetected = false;
    m_bStuckDetectionCompleted = false;

    // ĺĺ§ĺ?0ç§ĺčŞĺ¨ä¸ĺĺĄä˝ĺ¤çç¸ĺłĺé
    m_bAutoAscentStuckDetection = false;
    m_dAutoAscentStartTime = 0.0;
    m_uAutoAscentLastRounds = 0;
    m_bAutoAscentStuckHandling = false;
    m_bWaitingForTargetPosition = false;
    m_bWaitingForZeroComplete = false;
}

//---------------------------------------------------------
// ććĺ˝ć° - ĺŽĺ¨ĺłé­çşżç¨ĺç˝çťčżć?iLodging_HEU::~iLodging_HEU()
{
    // ĺć­˘ćĽćśçşżç¨
    m_bThreadRunning = false;
    // ĺˇć°çźĺ˛ĺşĺšśéĺşçşżç¨?    if (m_RecvThread != 0)
    {
        // ĺźşĺśĺˇć°ććçźĺ˛ĺş
        fflush(stdout);
        fflush(stderr);

        // ç´ćĽĺçŚťçşżç¨ďźčŽŠĺśčŞçśéĺ?        pthread_detach(m_RecvThread);
        m_RecvThread = 0;
    }
        // ĺĺłé­ĺĽćĽĺ­ďźä¸­ć­ĺŻč˝çéťĺĄćĽćśćä˝
    m_RecvSock.Close();
    m_SendSock.Close();
}

//---------------------------------------------------------
// ĺ¤çMOOSćśćŻ
bool iLodging_HEU::OnNewMail(MOOSMSG_LIST &NewMail)
{
    MOOSMSG_LIST::iterator p;
    for (p = NewMail.begin(); p != NewMail.end(); p++)
    {
        CMOOSMsg &msg = *p;
        string key = msg.GetKey();
        // CONTROL_MSGć źĺźĺ¤çćśćŻ

        if (key == "CONTROL_MSG")
        {
            // ćĽćśĺšśč§Łćć§ĺśćść?            string control_msg = msg.GetString();
            // č°ç¨TypeChoiceĺ˝ć°č§Łćć§ĺśćśćŻ
            TypeChoice(control_msg);
        }
        else if (key == "DESIRED_SPEED")
        {
            // ć´ć°ććéĺşŚ
            double old_speed = m_dDesiredSpeed;
            m_dDesiredSpeed = msg.GetDouble();

            // ĺŚćéĺşŚĺçĺĺä¸ĺ˝ĺä¸ćŻčŞĺ¨ć¨ĄĺźďźčŞĺ¨ĺć˘ĺ°čŞĺ¨ć¨Ąĺź?            if (old_speed != m_dDesiredSpeed && !m_bAutoControl && !m_bGFMode)
            {
                m_bAutoControl = true;
            }
        }
        else if (key == "Mainframestate")
        {
            // ć´ć°ä¸ťć§çść?            m_sMainframeState = msg.GetString();
            // ćŁćľä¸ťć§ĺ¤ąčďźčŞĺ¨ĺčľˇ
            if (m_sMainframeState == "The master has died")
            {
                m_bManualAscend = true;
                m_bManualDescend = false;
            }
        }
        else if (key == "CtrlMission_STATUS")
        {
            // ć´ć°äťťĺĄçść?            m_sMissionStatus = msg.GetString();
        }
        else if (key == "Depth")
        {
            // ć´ć°ĺ˝ĺćˇąĺşŚ
            m_dCurrentDepth = msg.GetDouble();
        }
        else if (key == "DESIRED_DEPTH")
        {
            // ć´ć°ćććˇąĺşŚ
            m_dDesiredDepth = msg.GetDouble();
        }
    }

    return true;
}

//---------------------------------------------------------
// čżćĽĺ°MOOSćĺĄĺ¨ĺć§čĄ
bool iLodging_HEU::OnConnectToServer()
{
    RegisterVariables();
    return (true);
}

//---------------------------------------------------------
// ĺ¨ćć§ć§čĄĺ˝ć°ďźĺ¤çć§ĺśéťčž
bool iLodging_HEU::Iterate()
{
    m_nIterations++;

    // 1. ćŁć?0ç§ĺŻĺ¨ĺťśčżéťčžďźĺŞćĺŻç¨CANčŽĄćśĺćĺźĺ§?    static bool s_bAutoStartupExecuted = false; // éćĺéďźčŽ°ĺ˝ćŻĺŚĺˇ˛ć§čĄčżčŞĺ¨ĺŻĺ¨
    // 50-60ç§éťčžč˝ĺ¤ć§čĄ
    if (!s_bAutoStartupExecuted && m_bCanTimingEnabled && m_dStartupTime > 0)
    {
        // čˇĺĺ˝ĺćśé´
        double currentTime = MOOSTime();
        // čŽĄçŽĺŻĺ¨ćśé´ĺˇ?        double elapsedTime = currentTime - m_dStartupTime;
        // ćŁćĽćśé´çŞĺ?        if (elapsedTime >= 50 && elapsedTime < 60)
        {
            MOOSTrace("č°čŻďźčżĺ?0-60ç§ćŁćľçŞĺŁďźçťčżćśé´=%.1fç§\n", elapsedTime);
            // 50s-60sĺéä¸éĺ°ĺşçŤŻçćäť?            if (!m_bStuckDetectionCompleted)
            {
                if (!m_bDescendCommandSent)
                {
                    // éŚćŹĄĺéä¸éćäť¤ďźčŽ°ĺ˝ćśé´ĺĺĺ§ä˝ç˝?                    m_bManualDescend = true;
                    m_bManualAscend = false;
                    m_bDescendCommandSent = true;
                    m_dDescendCommandTime = currentTime;
                    m_uLastRounds = m_uMotorRounds; // čŽ°ĺ˝ĺĺ§ĺć°
                    m_bStuckDetected = false;
                }
                else if (!m_bStuckDetected)
                {
                    // ćŁćľĺĄä˝ćĄäťśďźĺ¨ĺéćäť¤ĺç?ç§ĺčżčĄćŁćľ?                    double detectionTime = currentTime - m_dDescendCommandTime;
                    if (detectionTime <= 3.0)
                    {
                        // çľćľćŁćľďźä˝żç¨ć°ççľćľć°ćŽďźč˝Źć˘ä¸şĺŽĺš
                        double current_amperes = m_uMotorCurrent / 100.0;
                        bool current_exceeded = (current_amperes > m_dCurrentThreshold);

                        // ä˝ç˝ŽĺĺćŁćľďźĺć°ćŻĺŚäżćä¸ĺ
                        bool position_unchanged = (m_uMotorRounds == m_uLastRounds);

                        // çĄŽäżä¸ĺ¨ĺşé¨ćčżčĄĺĄä˝ĺ¤ć?                        bool not_at_bottom = !m_bAtBottom;



                        // ĺ¤ć­ćŻĺŚĺĄä˝ďźćŞĺ°ĺşé¨ä¸(çľćľčść ćä˝ç˝Žä¸ĺ?
                        if (not_at_bottom && (current_exceeded || position_unchanged))
                        {
                            // ĺéĺč˝Źäżćĺ˝ĺä˝ç˝Žćäť?                            SendHoldPositionCommand(m_uMotorRounds, m_uMotorDegrees, 0x02);

                            // ĺéć¸éśćäť?                            SendZeroCommand();

                            // ć čŽ°ĺĄä˝ĺˇ˛ĺ¤çĺćŁćľĺŽć?                            m_bStuckDetected = true;
                            m_bStuckDetectionCompleted = true;
                        }
                        else
                        {
                            // ć´ć°ä¸ćŹĄĺć°čŽ°ĺ˝ďźç¨äşä¸ćŹĄćŻčž?                            m_uLastRounds = m_uMotorRounds;
                        }
                    }
                    else
                    {
                        m_bStuckDetected = true;           // ć čŽ°ćŁćľĺŽćďźéżĺéĺ¤ćŁćľ?                        m_bStuckDetectionCompleted = true; // ć čŽ°ć´ä¸ŞćŁćľćľç¨ĺŽć?
                        // ćŞćŁćľĺ°ĺĄä˝ďźéç˝ŽĺŻĺ¨ĺťśčżć ĺżďźĺčŽ¸60ç§ĺçć­Łĺ¸¸čŞĺ¨ĺčľˇéťčž
                        m_bStartupDelayExecuted = false;
                    }
                }
            }
        }
        // ĺŚćĺŻĺ¨ćśé´ĺˇŽĺ¤§äşç­äşĺŻĺ¨ĺťśčżďźĺĺć˘ĺ°čŞĺ¨ć¨Ąĺźĺšść§čĄä¸ćŹĄä¸ĺ?        if (elapsedTime >= m_dStartupDelay)
        {
            // 60ç§ĺĺć˘ĺ°čŞĺ¨ć¨Ąĺźďźé¤éGFć¨ĄĺźćéŤéć¨Ąĺź?            if (!m_bGFMode && !IsHighSpeedMode())
            {
                // ĺć˘ĺ°čŞĺ¨ć¨Ąĺź?                m_bAutoControl = true;
                m_bGFMode = false;
                // čŽžç˝Žćĺ¨ä¸ĺć ĺżä˝ä¸ştrueďźćĺ¨ä¸éć ĺżä˝ä¸şfalseďźĺŻĺ¨ĺťśčżć ĺżä˝ä¸ştrue
                m_bManualAscend = true;
                m_bManualDescend = false;
                s_bAutoStartupExecuted = true;  // ć čŽ°čŞĺ¨ĺŻĺ¨ĺˇ˛ć§čĄ?                m_bStartupDelayExecuted = true; // äżćĺćéťčž

                // ĺŻĺ¨60ç§ĺčŞĺ¨ä¸ĺĺĄä˝ćŁćľ?                m_bAutoAscentStuckDetection = true;
                m_dAutoAscentStartTime = currentTime;
                m_uAutoAscentLastRounds = m_uMotorRounds;
                m_bAutoAscentStuckHandling = false;
            }
            else if (m_bGFMode)
            {
                s_bAutoStartupExecuted = true;  // ć čŽ°čŞĺ¨ĺŻĺ¨ĺˇ˛ć§čĄ?                m_bStartupDelayExecuted = true; // äżćĺćéťčž
            }
            else if (IsHighSpeedMode())
            {
                s_bAutoStartupExecuted = true;  // ć čŽ°čŞĺ¨ĺŻĺ¨ĺˇ˛ć§čĄ?                m_bStartupDelayExecuted = true; // äżćĺćéťčž
            }
        }
    }

    // 2. 60ç§ĺčŞĺ¨ä¸ĺĺĄä˝ćŁćľĺĺ¤çéťčž
    if (m_bAutoAscentStuckDetection && !m_bAutoAscentStuckHandling)
    {
        double currentTime = MOOSTime();
        double detectionTime = currentTime - m_dAutoAscentStartTime;

        // ćŁćľĺĄä˝ćĄäťśďźçľćľčść ćä˝ç˝Žä¸ĺďźćŁćľ?ç§ďź
        if (detectionTime <= 3.0 && !m_bAtTop)
        {
            // çľćľćŁćľďźä˝żç¨ć°ççľćľć°ćŽďźč˝Źć˘ä¸şĺŽĺš
            double current_amperes = m_uMotorCurrent / 100.0;
            bool current_exceeded = (current_amperes > m_dCurrentThreshold);

            // ä˝ç˝ŽĺĺćŁćľďźĺć°ćŻĺŚäżćä¸ĺ
            bool position_unchanged = (m_uMotorRounds == m_uAutoAscentLastRounds);

            // ĺ¤ć­ćŻĺŚĺĄä˝ďźćŞĺ°éĄśé¨ä¸(çľćľčść ćä˝ç˝Žä¸ĺ?
            if (!m_bAtTop && (current_exceeded || position_unchanged))
            {

                // čŽĄçŽçŽć ä˝ç˝Žďźĺ˝ĺä˝ç˝?- ćĺ¤§ä¸ĺč§ĺşŚďźäżçĺćéťčžďź?                double current_angle = ConvertComponentsToAngle(m_uMotorRounds, m_uMotorDegrees);
                double target_angle = current_angle - m_dMaxAscendAngle;

                // çĄŽäżçŽć č§ĺşŚä¸ĺ°äşćĺ°ä¸éč§ĺş?                if (target_angle < m_dMinDescendAngle)
                {
                    target_angle = m_dMinDescendAngle;
                }

                AngleComponents target_components = ConvertAngleToComponents(target_angle);

                // ĺéçŽć ä˝ç˝Žć§ĺśćäť¤ďźäżçĺćéťčžďź?                SendPositionCommand(target_components.rounds, target_components.degrees, 0x02);

                // ĺäźĺčŽŽćł¨éďźĺŽéĺşç´ćĽä¸éĺ°ćĺ°č§ĺş?                // double target_angle = m_dMinDescendAngle;
                // SendPositionCommand(0, (uint16_t)target_angle, 0x02);

                // čŽžç˝Žĺ¤ççść?                m_bAutoAscentStuckHandling = true;
                m_bWaitingForTargetPosition = true;
                m_bAutoAscentStuckDetection = false; // ĺć­˘ćŁćľ?            }
            else
            {
                // ć´ć°ä¸ćŹĄĺć°čŽ°ĺ˝
                m_uAutoAscentLastRounds = m_uMotorRounds;
            }
        }
        else if (detectionTime > 3.0)
        {
            m_bAutoAscentStuckDetection = false; // ĺć­˘ćŁćľ?        }
        else if (m_bAtTop)
        {
            m_bAutoAscentStuckDetection = false; // ĺć­˘ćŁćľ?        }
    }

    // 3. ćŁćľćĺ¨ć§ĺść ĺżä˝
    // ĺŚććĺ¨ä¸ĺć ĺżä˝ä¸ştrueďźĺć§čĄćĺ¨ä¸ĺć§ĺś
    if (m_bManualAscend)
    {
        MOOSTrace("ć§čĄćĺ¨ä¸ĺć§ĺś\n");
        ExecuteAscendControl();
        m_bManualAscend = false; // ć§čĄĺéç˝Žć ĺżä˝
    }

    // ĺŚććĺ¨ä¸éć ĺżä˝ä¸ştrueďźĺć§čĄćĺ¨ä¸éć§ĺś
    if (m_bManualDescend)
    {
        MOOSTrace("ć§čĄćĺ¨ä¸éć§ĺś\n");
        ExecuteDescendControl();
        m_bManualDescend = false; // ć§čĄĺéç˝Žć ĺżä˝

        // ćĺ¨ä¸éćść čŽ°ĺŻĺ¨ĺťśčżĺˇ˛ć§čĄďźé˛ć­?0ç§čŞĺ¨ćŹčľˇéťčžĺ˛çŞ
        if (!m_bStartupDelayExecuted)
        {
            m_bStartupDelayExecuted = true;
            MOOSTrace("ćĺ¨ä¸éć§čĄďźć čŽ°ĺŻĺ¨ĺťśčżĺˇ˛ć§čĄďźĺć­?0ç§čŞĺ¨ćŹčľˇ\n");
        }
    }

    // 3. čŞĺ¨ć§ĺśéťčžďźĺŞćĺŻç¨ćśćčżčĄ?    if (m_bAutoControl)
    {
        ExecuteAutoControl();
    }

    // 4. ćŁćľGFć¨Ąĺźć ĺżä˝?    // ĺŚćGFć¨Ąĺźć ĺżä˝ä¸ştrueďźĺć§čĄGFć¨Ąĺź
    if (m_bGFMode)
    {
        ExecuteGFMode();
    }

    // ĺĺ¸çťä¸çLIFTçśćďźäźĺçş§ďźMOTION > TOP > BOTTOM > MIDDLEďź?    std::string lift_status;
    if (m_bInMotion)
    {
        lift_status = "MOTION";
    }
    else if (m_bAtTop)
    {
        lift_status = "TOP";
    }
    else if (m_bAtBottom)
    {
        lift_status = "BOTTOM";
    }
    else if (m_bStuckInMiddle)
    {
        lift_status = "MIDDLE";
    }
    else
    {
        lift_status = " "; // éťčŽ¤çść?    }

    Notify("LIFT_Status", lift_status);

    // ćçť­ĺĺ¸çľćşçśćĺé?    Notify("MOTOR_TOTAL_ANGLE", m_dMotorTotalAngle);
    Notify("MOTOR_ROUNDS", (double)m_uMotorRounds);
    Notify("MOTOR_DEGREES", (double)m_uMotorDegrees);
    Notify("MOTOR_CURRENT", (double)m_uMotorCurrent);
    Notify("MOTOR_STATE", (double)m_uMotorState);
    Notify("MOTOR_ERROR", (double)m_uMotorError);

    // ĺ¨Iterateä¸­ćŁćĽGFć¨ĄĺźćŻĺŚĺşčŻĽéĺşďźĺŞćçć­ŁĺŽćä¸ĺä¸ĺ°čžžéĄśé¨ćéĺşďź
    if (m_bGFMode && m_bAscendInProgress && m_bAtTop)
    {
        // GFć¨Ąĺźć§čĄĺŽćä¸ĺˇ˛ĺ°čžžéĄśé¨ďźç´ćĽĺć˘ĺ°čŞĺ¨ć¨Ąĺź
        MOOSTrace("GFć¨ĄĺźďźćŁćľĺ°ĺˇ˛ĺ°čžžéĄśé¨ďźçŤĺłéĺşGFć¨Ąĺźďźĺć˘ĺ°čŞĺ¨ć§ĺść¨Ąĺź\n");
        m_bGFMode = false;
        m_bAutoControl = true;
    }

    return true;
}

//---------------------------------------------------------
// ĺşç¨ĺŻĺ¨ćść§čĄďźĺ č˝˝éç˝Ž
bool iLodging_HEU::OnStartUp()
{
    // äťéç˝Žćäťśčˇĺç˝çťĺć?    string sRecvIP;
    if (!m_MissionReader.GetConfigurationParam("RecvIP", sRecvIP))
    {
        MOOSTrace("ćŞćĺŽRecvIPĺć°ďźä˝żç¨éťčŽ¤ĺ?.0.0.0\n");
        sRecvIP = "0.0.0.0";
    }

    int iRecvPort;
    if (!m_MissionReader.GetConfigurationParam("RecvPort", iRecvPort))
    {
        MOOSTrace("éčŻŻ: ĺżéĄťćĺŽćĽćśçŤŻĺŁRecvPort\n");
    }

    string sDestIP;
    if (!m_MissionReader.GetConfigurationParam("DestIP", sDestIP))
    {
        MOOSTrace("éčŻŻ: ĺżéĄťćĺŽçŽć IPĺ°ĺDestIP\n");
    }

    int iDestPort;
    if (!m_MissionReader.GetConfigurationParam("DestPort", iDestPort))
    {
        MOOSTrace("éčŻŻ: ĺżéĄťćĺŽçŽć çŤŻĺŁDestPort\n");
    }

    // čŻťĺéŤééĺźéç˝Žĺć°ďźćĽčŞäşéç˝Žćäť?    m_MissionReader.GetConfigurationParam("HIGH_SPEED", m_dHighSpeed);

    // čŻťĺçľćľéĺźéç˝Žĺć°ďźćĽčŞäşéç˝Žćäť?    m_MissionReader.GetConfigurationParam("CURRENT_THRESHOLD", m_dCurrentThreshold);

    // čŻťĺč§ĺşŚéç˝Žĺć°ďźćĺ¤§ä¸ĺč§ĺşŚĺćĺ°ä¸éč§ĺşŚďźćĽčŞäşéç˝Žćäť?    m_MissionReader.GetConfigurationParam("MAX_ASCEND_ANGLE", m_dMaxAscendAngle);
    m_MissionReader.GetConfigurationParam("MIN_DESCEND_ANGLE", m_dMinDescendAngle);

    // čŻťĺĺäźçľćşĺžŽč°ĺć°
    m_MissionReader.GetConfigurationParam("ZERO_OFFSET", m_dZeroOffset);

    // čŻťĺĺŻĺ¨ĺťśčżéç˝ŽďźćĽčŞäşéç˝Žćäťś
    m_MissionReader.GetConfigurationParam("STARTUP_DELAY", m_dStartupDelay);

    // čŻťĺéčŻéç˝Žĺć°ďźćĽčŞäşéç˝Žćäťś
    int maxRetries = 5;
    int retryDelay = 2;
    m_MissionReader.GetConfigurationParam("MaxRetries", maxRetries);
    m_MissionReader.GetConfigurationParam("RetryDelay", retryDelay);

    // čŽ°ĺ˝ç¨ĺşĺŻĺ¨ćśé´ďźčżčžšćŻčŽ°ĺ˝ç¨ĺşĺŻĺ¨ćśé´ďźç¨äşĺŻĺ¨ĺťśčżéťčž
    m_dStartupTime = MOOSTime();

    MOOSTrace("éĺşŚéç˝Ž: éŤé?%.1fmm/s\n", m_dHighSpeed);
    MOOSTrace("çľćľéç˝Ž: ĺĄä˝ćŁćľéĺ?%.1fA\n", m_dCurrentThreshold);

    // ćžç¤şč§ĺşŚéç˝ŽĺčŞĺ¨čŽĄçŽçĺć°ďźäżçĺćéťčžďź?    AngleComponents max_components = ConvertAngleToComponents(m_dMaxAscendAngle);
    AngleComponents min_components = ConvertAngleToComponents(m_dMinDescendAngle);
    MOOSTrace("č§ĺşŚéç˝Ž: ćĺ¤§ä¸ĺ?%.1fĺş?%dĺ?%dĺş?, ćĺ°ä¸é?%.1fĺş?%dĺ?%dĺş?\n",
              m_dMaxAscendAngle, max_components.rounds, max_components.degrees,
              m_dMinDescendAngle, min_components.rounds, min_components.degrees);

    // ĺäźĺčŽŽćł¨éďźĺŽéĺşäťćžç¤şč§ĺşŚčĺ?    // MOOSTrace("ĺäźçľćşč§ĺşŚéç˝Ž: ćĺ¤§ä¸ĺ?%.1fÂ°, ćĺ°ä¸é?%.1fÂ°ďźĺčŽŽčĺ?Â°-90Â°ďź\n",
    //           m_dMaxAscendAngle, m_dMinDescendAngle);
    MOOSTrace("ĺŻĺ¨ĺťśčżéç˝Ž: %.1fç§ĺčŞĺ¨ĺčľˇ\n", m_dStartupDelay);

    // ĺĺ§ĺç˝çť?- ä˝żç¨éčŻćşĺś
    if (!m_RecvSock.OpenSocketWithRetry(sRecvIP, iRecvPort, maxRetries, retryDelay))
    {
        // čżćŻçłťçťçş§çĺč­Śďźĺ˝MOOSéç˝ŽćäťśčŻťĺĺ¤ąč´Ľćśč§Śĺ?        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=0;LEVEL=FATAL;NOTE=MOOS File Read Failed");
        MOOSTrace("éčŻŻ: ć ćłćĺźćĽćśĺĽćĽĺ­ďźĺˇ˛éčŻ?dćŹĄďźç¨ĺşĺ°çť§çť­čżčĄ\n", maxRetries);
    }

    // ĺĺ°čŻć­Łĺ¸¸çťĺŽďźĺ¤ąč´ĽĺĺéčŻ
    if (!m_RecvSock.BindSocket())
    {
        MOOSTrace("éŚćŹĄçťĺŽĺ¤ąč´Ľďźĺźĺ§éčŻ?..\n");
        if (!m_RecvSock.RebindSocket(maxRetries, retryDelay))
        {
            // čżćŻç˝çťéäżĄçĺč­Śďźĺ˝ç˝çťĺĽćĽĺ­çťĺŽĺ¤ąč´Ľćśč§Śĺ?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=1;LEVEL=FATAL;NOTE=Network Binding Failed");
            MOOSTrace("éčŻŻ: ć ćłçťĺŽćĽćśĺĽćĽĺ­ďźĺˇ˛éčŻ?dćŹĄďźç¨ĺşĺ°çť§çť­čżčĄ\n", maxRetries);
            m_RecvSock.Close(); // ĺłé­ĺˇ˛ćĺźçĺĽćĽĺ­
        }
    }

    // čŽžç˝ŽćĽćśčśćśä¸?ç§ďźé˛ć­˘çşżç¨éťĺĄ
    if (!m_RecvSock.SetReceiveTimeout(1, 0))
    {
        MOOSTrace("č­Śĺ: ć ćłčŽžç˝ŽćĽćśĺĽćĽĺ­čśćś\n");
    }

    if (!m_SendSock.OpenSocketWithRetry(sDestIP, iDestPort, maxRetries, retryDelay))
    {
        // čżćŻéäżĄçş§çĺč­Śďźĺ˝ç˝çťéäżĄčśćśćśč§Śĺ?        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=3;LEVEL=FATAL;NOTE=Network Communication Timeout");
        MOOSTrace("éčŻŻ: ć ćłćĺźĺéĺĽćĽĺ­ďźĺˇ˛éčŻ%dćŹĄďźç¨ĺşĺ°çť§çť­čżčĄ\n", maxRetries);
        m_RecvSock.Close(); // ĺłé­ĺˇ˛ćĺźçćĽćśĺĽćĽĺ­
    }

    // ĺĺťşćĽćśçşżç¨
    m_bThreadRunning = true;
    if (pthread_create(&m_RecvThread, NULL, RecvThreadWrapper, this) != 0)
    {
        // čżćŻçłťçťçş§çĺč­Śďźĺ˝ćĽćśçşżç¨ĺĺťşĺ¤ąč´Ľćśč§Śĺ?        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=2;LEVEL=FATAL;NOTE=Thread Creation Failed");
        MOOSTrace("éčŻŻ: ć ćłĺĺťşćĽćśçşżç¨ďźç¨ĺşĺ°çť§çť­čżčĄ\n");
        m_bThreadRunning = false;
        // ć¸çĺˇ˛ćĺźçĺĽćĽĺ­
        m_RecvSock.Close();
        m_SendSock.Close();
    }
    MOOSTrace("ćĽćśçşżç¨ĺĺťşćĺ\n");

    m_dTimewarp = GetMOOSTimeWarp();
    RegisterVariables();

    return true;
}

//---------------------------------------------------------
// ćł¨ĺMOOSĺé
void iLodging_HEU::RegisterVariables()
{
    // ćł¨ĺć§ĺśćśćŻĺéĺşŚĺéďźćĽčŞäşĺśäťć¨Ąĺ
    Register("CONTROL_MSG", 0);
    Register("DESIRED_SPEED", 0);
    Register("Mainframestate", 0);

    // ćł¨ĺäťťĺĄçśćĺéďźćĽčŞäşĺśäťć¨Ąĺ?    Register("CtrlMission_STATUS", 0);

    // ćł¨ĺćˇąĺşŚĺéďźĺ˝ĺćˇąĺşŚĺćććˇąĺşŚďźćĽčŞäşĺśäťć¨Ąĺ
    Register("Depth", 0);
    Register("DESIRED_DEPTH", 0);
}

//---------------------------------------------------------
// Procedure: RecvThreadWrapper - çşżç¨ĺčŁĺ˝ć°
void *iLodging_HEU::RecvThreadWrapper(void *arg)
{
    iLodging_HEU *pThis = static_cast<iLodging_HEU *>(arg);
    pThis->RecvFrame();
    return NULL;
}

//---------------------------------------------------------
// Procedure: RecvFrame - äťç˝çťćĽćśĺ¸§ć°ćŽ
void iLodging_HEU::RecvFrame()
{
    MOOSTrace("ćĽćśçşżç¨ĺźĺ§čżčĄďźä˝żç¨recvfroméťĺĄIOďź\n");

    while (m_bThreadRunning)
    {
        // ä˝żç¨BlueSocketçRecvBinaryćšćłćĽćść°ćŽ
        vector<uint8_t> Frame;
        int n = m_RecvSock.RecvBinary(Frame, FRAME_LEN);

        if (n <= 0)
        {
            // ćŁćĽçşżç¨ćŻĺŚĺşčŻĽéĺ?            if (!m_bThreadRunning)
            {
                MOOSTrace("ćĽćśçşżç¨ďźćśĺ°éĺşäżĄĺˇ\n");
                break;
            }

            // ćĽćśĺ¤ąč´Ľćčśćśďźçť§çť­ä¸ä¸ćŹĄĺžŞç?            continue;
        }

        // ćĺćĽćśĺ°ć°ćŽďźč§Łćĺ¸§ć°ć?        ParseFrame(Frame);

        // ćŁćĽçşżç¨ćŻĺŚĺşčŻĽéĺ?        if (!m_bThreadRunning)
        {
            break;
        }
    }

    MOOSTrace("ćĽćśçşżç¨çťć\n");
}

//---------------------------------------------------------
// Procedure: ParseFrame - č§ŁććĽćśĺ°çĺ¸§ć°ć?void iLodging_HEU::ParseFrame(vector<uint8_t> Frame)
{
    // ĺŚćĺ¸§éżĺşŚä¸ç­äş13ĺ­čďźĺčżĺ
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("ĺ¸§éżĺşŚć ć? %d\n", Frame.size());
        return;
    }
    // č§Łć13ĺ­čç˝č˝ŹCANĺ¸§ć°ćŽďźĺĺŤćŻĺ¸§ĺ¤´ďźCAN IDďźć§ĺść¨Ąĺźďźčżĺ¨çśćďźĺ˝ĺçľćľďźĺ˝ĺĺć°ďźĺ˝ĺĺşŚć°ďźéčŻŻçść?    uint8_t frame_header = Frame[0]; // ĺ¸§ĺ¤´ 0x08
    uint32_t can_id = (Frame[1] << 24) | (Frame[2] << 16) |
                      (Frame[3] << 8) | Frame[4]; // CAN ID (32ä˝?

    // ĺŞĺ¤çćĽčŞć§čĄĺ¨ĺéŚID(0x112)çć°ć?    if (can_id == 0x112)
    {
        // ćśĺ°çŹŹä¸ä¸ŞCANćĽćďźĺŻç¨čŽĄć?        if (!m_bCanTimingEnabled)
        {
            m_bCanTimingEnabled = true;
            MOOSTrace("ćśĺ°çŹŹä¸ä¸ŞCAN ID 0x112ćĽćďźĺŻç¨MOOSTimečŽĄćś\n");
        }

        uint8_t control_mode = Frame[5];                         // ć§ĺść¨Ąĺź
        uint8_t motion_state = Frame[6];                         // čżĺ¨çść?        uint16_t current_current = (Frame[7] << 8) | Frame[8];   // ĺ˝ĺçľćľďźĺ­č?-3ĺ¨ĺčŽŽä¸­ďźä˝ĺ¨Frameä¸­ćŻ7-8ďź?        uint8_t current_rounds = Frame[9];                       // ĺ˝ĺĺć°
        uint16_t current_degrees = (Frame[10] << 8) | Frame[11]; // ĺ˝ĺĺşŚć°
        uint8_t error_state = Frame[12];                         // éčŻŻçść?
        // čŽĄçŽćťč§ĺşŚďźäżçĺćéťčžäťĽé˛ĺźĺŽšć§éčŚďź
        double total_angle = ConvertComponentsToAngle(current_rounds, current_degrees);

        // ĺäźĺčŽŽćł¨éďźĺŽéĺşç´ćĽä˝żç¨č§ĺşŚĺźďźĺż˝çĽĺć°ďźč§ĺşŚčĺ?Â°-90Â°ďź?        // double current_angle = (double)current_degrees;

        // ć´ć°ćĺĺé
        m_dMotorTotalAngle = total_angle;
        m_uMotorRounds = current_rounds;
        m_uMotorDegrees = current_degrees;
        m_uMotorCurrent = current_current;  // ć´ć°ä¸şçľćľć°ć?        m_uMotorState = motion_state;
        m_uMotorError = error_state;

        // ĺ¤ççľćşéčŻŻç ĺšśĺéĺč­?        ProcessMotorError(error_state);

        // ć´ć°çśćć ĺżä˝
        UpdateElevatorStatus(motion_state, current_rounds, current_degrees, current_current, error_state);

        // ĺ ľč˝Źäżć¤éťčžďźćŁćľĺ°ĺ ľč˝Źćśçĺ¤ç
        if (error_state == ERROR_STALL)
        {
            // ĺŚćĺ¨GFć¨Ąĺźä¸ć­Łĺ¨ä¸ĺčżç¨ä¸­ďźçŤĺłä¸é?            if (m_bGFMode && m_bAscendInProgress)
            {
                MOOSTrace("ĺ ľč˝Źäżć¤ďźGFć¨Ąĺźä¸ĺĺ ľč˝ŹďźçŤĺłĺć˘ä¸şä¸é\n");
                m_bManualDescend = true;
                m_bManualAscend = false;
                m_bAscendInProgress = false;
                MOOSTrace("GFć¨ĄĺźďźćŁćľĺ°ĺ ľč˝Źçć§čĄďźĺéä¸éćäť¤ďźčżç§ćĺľä¸ĺ°ąĺŤĺčŽŠäťä¸ĺäş\n");
            }
            // ĺŚćĺ¨ĺśäťć¨Ąĺźä¸ĺ ľč˝Źďźĺć­˘ĺ˝ĺćä˝?            else if (motion_state != STATE_IDLE)
            {
                MOOSTrace("ĺ ľč˝Źäżć¤ďźćŁćľĺ°ĺ ľč˝Źďźĺéäżćä˝ç˝Žĺ˝äť¤ĺć­˘čżĺ¨\n");
                SendHoldPositionCommand(current_rounds, current_degrees, 0x02); // ä˝żç¨ĺäźçľćşIDĺć­˘
            }
        }

        // GFć¨Ąĺźä¸çĺčľˇćĺ/ĺ¤ąč´ĽćŁćľ?        if (m_bGFMode && m_bAscendInProgress)
        {
            // čˇĺçŽć č§ĺşŚçĺć°ĺč§ĺşŚďźäżçĺćéťčžďź?            AngleComponents target = ConvertAngleToComponents(m_dMaxAscendAngle);

            // ĺ¤ć­ćŻĺŚčžžĺ°çŽć ä˝ç˝Žďźĺć°ä¸č´ďźč§ĺşŚčŻŻĺˇŽĺ?ĺşŚäťĽĺďź
            bool ascend_success = (current_rounds == target.rounds) &&
                                  (abs((int)current_degrees - (int)target.degrees) <= 5);

            // ĺäźĺčŽŽćł¨éďźĺŽéĺşç´ćĽćŻčžč§ĺşŚĺ?            // bool ascend_success = (abs((double)current_degrees - m_dMaxAscendAngle) <= 5.0);

            if (ascend_success)
            {
                MOOSTrace("GFć¨Ąĺźďźĺčľˇćĺďźĺ˝ĺä˝ç˝Ž(%dĺ?%dĺş?čžžĺ°çŽć ä˝ç˝Ž(%dĺ?%dĺş?\n",
                          current_rounds, current_degrees, target.rounds, target.degrees);
                // ĺäźĺčŽŽćł¨éďźĺŽéĺşćžç¤şč§ĺşŚäżĄćŻ
                // MOOSTrace("GFć¨Ąĺźďźĺäźĺčľˇćĺďźĺ˝ĺč§ĺş?.1fÂ°čžžĺ°çŽć č§ĺşŚ%.1fÂ°\n",
                //           (double)current_degrees, m_dMaxAscendAngle);
                MOOSTrace("GFć¨ĄĺźďźéĺşGFć¨Ąĺźďźĺć˘ĺ°čŞĺ¨ć§ĺść¨Ąĺź\n");

                // ĺčľˇćĺďźéĺşGFć¨ĄĺźďźĺŻç¨čŞĺ¨ć§ĺ?                m_bGFMode = false;
                m_bAutoControl = true;
                m_bAscendInProgress = false;
                m_bManualAscend = false;
                m_bManualDescend = false;
            }
            else if (error_state != ERROR_NONE || m_bStuckInMiddle)
            {
                // çšĺŤĺ¤çĺ ľč˝Źćĺľ
                if (error_state == ERROR_STALL)
                {
                    MOOSTrace("GFć¨ĄĺźďźćŁćľĺ°ä¸ĺĺ ľč˝ŹďźçŤĺłĺéä¸éćäť¤\n");
                }
                else
                {
                    MOOSTrace("GFć¨Ąĺźďźĺčľˇĺ¤ąč´ĽďźćŁćľĺ°éčŻŻ(éčŻŻç ?%d)ćĺĄĺ¨ä¸­é´\n", error_state);
                }

                MOOSTrace("GFć¨Ąĺźďźĺéä¸éćäť¤ďźäżćGFć¨Ąĺź\n");

                // ĺčľˇĺ¤ąč´Ľďźĺéä¸éćäť¤ďźäżćGFć¨Ąĺź
                m_bManualDescend = true;
                m_bManualAscend = false;
                m_bAscendInProgress = false;
                // m_bGFMode äżćä¸?true
            }
        }

        // MOTORĺéçĺĺ¸ĺˇ˛ç§ťčłIterateĺ˝ć°ä¸­ďźçĄŽäżćçť­ĺĺ¸

        MOOSTrace("ćśĺ°ĺéŚĺ¸?CAN_ID=0x112): ć¨Ąĺź=0x%02X, çść?%d, çľćľ=%d, ĺć°=%d, ĺşŚć°=%d, ćťč§ĺş?%.1fÂ°, éčŻŻ=%d\n",
                  control_mode, motion_state, current_current, current_rounds, current_degrees, total_angle, error_state);
    }
    else
    {
        MOOSTrace("ćśĺ°éĺéŚIDçć°ćŽĺ¸§: CAN_ID=0x%08Xďźĺż˝çĽĺ¤ç\n", can_id);
    }
}

//---------------------------------------------------------
// Procedure: UpdateElevatorStatus - ć´ć°ĺéćşćçść?void iLodging_HEU::UpdateElevatorStatus(uint8_t motion_state, uint8_t rounds, uint16_t degrees, uint16_t current, uint8_t error)
{
    // čŽĄçŽĺ˝ĺćťč§ĺşŚďźäżçĺćéťčžďź?    double current_total_angle = ConvertComponentsToAngle(rounds, degrees);

    // čŽĄçŽçŽć č§ĺşŚççťäťśďźäżçĺćéťčžďź?    AngleComponents max_target = ConvertAngleToComponents(m_dMaxAscendAngle);
    AngleComponents min_target = ConvertAngleToComponents(m_dMinDescendAngle);

    // ĺ¤ć­ćŻĺŚĺ¨éĄśé¨ďźćĽčżćĺ¤§ä¸ĺč§ĺşŚďź
    double max_total_angle = ConvertComponentsToAngle(max_target.rounds, max_target.degrees);
    m_bAtTop = (current_total_angle >= (max_total_angle - 10.0)); // 10ĺşŚĺŽšĺˇ?
    // ĺäźĺčŽŽćł¨éďźĺŽéĺşç´ćĽä˝żç¨č§ĺşŚćŻčž
    // double current_angle = (double)degrees;
    // m_bAtTop = (current_angle >= (m_dMaxAscendAngle - 5.0));

    // ĺ¤ć­ćŻĺŚĺ¨ĺşé¨ďźćĽčżćĺ°ä¸éč§ĺşŚďź
    double min_total_angle = ConvertComponentsToAngle(min_target.rounds, min_target.degrees);
    m_bAtBottom = (current_total_angle <= min_total_angle); // ç˛žçĄŽĺ¤ć­ďźä¸ĺ ĺŽšĺˇ?
    // ĺäźĺčŽŽćł¨éďźĺŽéĺşç´ćĽä˝żç¨č§ĺşŚćŻčž
    // m_bAtBottom = (current_angle <= (m_dMinDescendAngle + 5.0));
    // ĺ¤ć­ćŻĺŚĺ¨čżĺ¨ďźçľćľĺ¤§äş0ćčżĺ¨çśćä¸ćŻéć­˘ďź
    m_bInMotion = (current > 0 || motion_state != STATE_IDLE); // STATE_IDLE=2=éć­˘çść?
    // ĺ¤ć­ćŻĺŚĺĄĺ¨ä¸­é´ďźćéčŻŻä¸ä¸ĺ¨éĄśé¨äšä¸ĺ¨ĺşé¨ďź?    m_bStuckInMiddle = (error != ERROR_NONE && !m_bAtTop && !m_bAtBottom);

    // čŞĺ¨äżćä˝ç˝Žéťčž
    if (m_bAtTop && motion_state != STATE_IDLE)
    {
        // ĺ°čžžéĄśé¨ćśďźä˝żç¨ĺäźçľćşID (0x02) ĺéäżćä˝ç˝Žĺ˝äť?        // SendHoldPositionCommand(rounds, degrees, 0x02);

        // ĺ°čžžéĄśé¨ĺďźçŤĺłĺ°čżĺ¨ć ĺżä˝čŽžç˝Žä¸şfalse
        m_bInMotion = false;
    }
    else if (m_bAtBottom && motion_state != STATE_IDLE)
    {
        // ĺ°čžžĺşé¨ćśďźä˝żç¨ĺäźçľćşID (0x02) ĺéäżćä˝ç˝Žĺ˝äť?        SendHoldPositionCommand(rounds, degrees, 0x02);

        // ĺéäżćä˝ç˝Žĺ˝äť¤ĺďźçŤĺłĺ°čżĺ¨ć ĺżä˝čŽžç˝Žä¸şfalse
        m_bInMotion = false;
    }
    // ĺĄä˝ĺ¤ççśććŁć?    if (m_bAutoAscentStuckHandling)
    {
        if (m_bWaitingForTargetPosition && motion_state == STATE_IDLE)
        {
            MOOSTrace("ĺˇ˛ĺ°čžžĺĄä˝ĺ¤ççŽć ä˝ç˝Žďźĺéć¸éśćäť¤\n");
            SendZeroCommand();
            m_bWaitingForTargetPosition = false;
            m_bWaitingForZeroComplete = true;
        }
        else if (m_bWaitingForZeroComplete && rounds == 0 && degrees == 0)
        {
            MOOSTrace("ć¸éśĺŽćďźć˘ĺ¤čŞĺ¨ć¨Ąĺźçť§çť­ä¸ĺĺ°éĄśé¨\n");
            // ć˘ĺ¤čŞĺ¨ä¸ĺć¨Ąĺź
            m_bManualAscend = true;
            m_bManualDescend = false;
            m_bAutoControl = true;

            // éç˝ŽĺĄä˝ĺ¤ççść?            m_bAutoAscentStuckHandling = false;
            m_bWaitingForZeroComplete = false;

            // éć°ĺŻĺ¨ĺĄä˝ćŁćľ?            m_bAutoAscentStuckDetection = true;
            m_dAutoAscentStartTime = MOOSTime();
            m_uAutoAscentLastRounds = rounds;

            MOOSTrace("ĺĄä˝ĺ¤çĺŽćďźéć°ĺŻĺ¨čŞĺ¨ä¸ĺĺĺĄä˝ćŁćľ\n");
        }
    }
}

//---------------------------------------------------------
// Procedure: ProcessMotorError - ĺ¤ççľćşéčŻŻç ĺšśĺéĺč­?void iLodging_HEU::ProcessMotorError(uint8_t can_error_code)
{
    // ä˝żç¨éćĺéčŽ°ĺ˝ä¸ćŹĄéčŻŻç ďźéżĺéĺ¤ĺéç¸ĺĺč­?    static uint8_t last_error_code = 0xFF;

    // ĺŞćĺ˝éčŻŻç ĺçĺĺćśćĺéĺč­?    if (can_error_code != last_error_code)
    {
        switch (can_error_code)
        {
        case 0: // ć ĺźĺ¸?            if (last_error_code != 0xFF && last_error_code != 0)
            {
                MOOSTrace("çľćşéčŻŻĺˇ˛ć˘ĺ¤ďźäťéčŻŻç %dć˘ĺ¤ĺ°ć­Łĺ¸¸çść\n", last_error_code);
            }
            break;

        case 1: // čżç­
            // čżćŻçľćşçĄŹäťśçĺč­Śďźĺ˝çľćşć¸ŠĺşŚčżéŤćśč§ŚĺďźĺŻšĺşCANĺčŽŽéčŻŻç ?ďź?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=4;LEVEL=FATAL;NOTE=Motor Overheating");
            MOOSTrace("ĺéçľćşčżç­ĺč­?(CANéčŻŻç ?1)\n");
            break;

        case 2: // čżćľ
            // čżćŻçľćşçĄŹäťśçĺč­Śďźĺ˝çľćşçľćľčżĺ¤§ćśč§ŚĺďźĺŻšĺşCANĺčŽŽéčŻŻç ?ďź?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=5;LEVEL=FATAL;NOTE=Motor Overcurrent");
            MOOSTrace("ĺéçľćşčżćľĺč­?(CANéčŻŻç ?2)\n");
            break;

        case 3: // çľĺčżä˝
            // čżćŻçľćşçĄŹäťśçĺč­Śďźĺ˝çľćşçľĺčżä˝ćśč§ŚĺďźĺŻšĺşCANĺčŽŽéčŻŻç ?ďź?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=6;LEVEL=WARN;NOTE=Motor Undervoltage");
            MOOSTrace("ĺéçľćşćŹ ĺĺč­?(CANéčŻŻç ?3)\n");
            break;

        case 4: // çźç ĺ¨éčŻ?            // čżćŻçľćşçĄŹäťśçĺč­Śďźĺ˝çźç ĺ¨ĺşç°éčŻŻćśč§ŚĺďźĺŻšĺşCANĺčŽŽéčŻŻç ?ďź?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=7;LEVEL=FATAL;NOTE=Encoder Error");
            MOOSTrace("ĺéçźç ĺ¨éčŻŻĺč­Ś (CANéčŻŻç ?4)\n");
            break;

        case 5: // ĺ ľč˝Ź
            // čżćŻçľćşçĄŹäťśçĺč­Śďźĺ˝çľćşĺçĺ ľč˝Źćśč§ŚĺďźĺŻšĺşCANĺčŽŽéčŻŻç ?ďź?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=8;LEVEL=FATAL;NOTE=Motor Stall Detected");
            MOOSTrace("ĺéçľćşĺ ľč˝Źĺč­?(CANéčŻŻç ?5)\n");
            break;

        case 6: // ĺšč˝ŚçľĺčżéŤ
            // čżćŻçľćşçĄŹäťśçĺč­Śďźĺ˝ĺšč˝Śçľĺĺźĺ¸¸ćśč§ŚĺďźĺŻšĺşCANĺčŽŽéčŻŻç ?ďź?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=9;LEVEL=WARN;NOTE=Brake Voltage Error");
            MOOSTrace("ĺéĺšč˝Śçľĺĺźĺ¸¸ĺč­?(CANéčŻŻç ?6)\n");
            break;

        case 7: // DRVéŠąĺ¨éčŻŻ
            // čżćŻçľćşçĄŹäťśçĺč­Śďźĺ˝éŠąĺ¨ĺ¨ĺçćéćśč§ŚĺďźĺŻšĺşCANĺčŽŽéčŻŻç ?ďź?            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=10;LEVEL=FATAL;NOTE=DRV Driver Error");
            MOOSTrace("ĺééŠąĺ¨ĺ¨ćéĺč­Ś (CANéčŻŻç ?7)\n");
            break;

        default: // ćŞçĽéčŻŻç ?            MOOSTrace("ćśĺ°ćŞçĽçľćşéčŻŻç ? %d\n", can_error_code);
            break;
        }

        // ć´ć°ä¸ćŹĄéčŻŻç ?        last_error_code = can_error_code;
    }
}

//---------------------------------------------------------
// Procedure: SendFrame - ĺéĺ¸§ć°ćŽ
void iLodging_HEU::SendFrame(vector<uint8_t> Frame)
{
    int result = m_SendSock.SendBinary(Frame);
    if (result < 0)
    {
        MOOSTrace("ĺéć°ćŽĺ¤ąč´Ľ\n");
    }
    else
    {
        MOOSTrace("ĺéć°ćŽćĺďźéżĺşŚ: %d\n", Frame.size());
    }
}

//---------------------------------------------------------
// Procedure: TypeChoice - č§Łćć§ĺśćśćŻçąťĺ
bool iLodging_HEU::TypeChoice(std::string param)
{
    // č§Łćĺéçľćşć§ĺśćśćŻć źĺź:
    // ćĺ¨ć§ĺś: MsgType=control;Act=function;Type=motion;Mode=Ascend/Descend;
    // čŞĺ¨ć§ĺś: MsgType=control;Act=function;Type=Autorise;Enable=yes/no;
    // GFć¨Ąĺź: MsgType=control;Act=function;Type=GF;Enable=yes/no;
    // ĺć­˘ĺčľˇ(GFć¨Ąĺź): MsgType=control;Act=function;Type=stop;Enable=no;
    // ĺŻźčŞĺŻĺ¨: Type=function;Act=NavStart;
    // éŤéčŞčĄĺŻč? Type=motion;Act=Start;
    // č§ĺşŚć¨Ąĺź: MsgType=control;ACT=function;TYPE=motion;Mode=MotorSetAngleMode;
    // çé˘ć¸éśćäť¤ďźMsgType=control;ACT=function;TYPE=motion;Reset=yes;

    MOOSTrace("=== TypeChoice ĺźĺ§č§Łćĺéçľćşĺ˝äť?===\n");
    MOOSTrace("čžĺĽĺ˝äť¤: [%s]\n", param.c_str());

    // čżćŻĺŻćŹ
    std::string sStr = param;
    // č˝Źć˘ä¸şĺ¤§ĺ?    MOOSToUpper(sStr);
    // ĺťé¤ĺ­çŹŚä¸˛ä¸¤çŤŻççŠşć ź
    MOOSTrimWhiteSpace(sStr);

    // äżĺ­çťć
    std::string sOneParam;
    // ĺ­ĺ¨ĺć°ĺç§°ďźéŽďź?    std::string sParamName;
    // ĺ­ĺ¨ĺć°ĺźďźĺźďź
    std::string sParamVal;

    // çŹŹä¸ć­ĽďźćŁćĽćŻĺŚä¸şçŽĺć źĺźĺ˝äť¤ďźć˛ĄćMsgTypeĺçźďź?    // ĺĺ˛çŹŹä¸ä¸Şďź
    sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is first param
    // ĺĺ˛çŹŹä¸ä¸?
    sParamName = MOOSChomp(sOneParam, "=");
    // ćżĺ°éŽĺ?    sParamVal = sOneParam;

    // ćŁćĽćŻĺŚä¸şçŽĺć źĺźĺ˝äť?(Type=xxx;Act=xxx)
    if (sParamName == "TYPE")
    {
        std::string sTypeVal = sParamVal;

        // č§ŁćActĺć°
        sOneParam = MOOSChomp(sStr, ";"); // chomp the next ";" ,result is Act
        sParamName = MOOSChomp(sOneParam, "=");
        std::string sActVal = sOneParam;

        if (sParamName == "ACT")
        {
            // ćŁćĽĺŻźčŞĺŻĺ¨ĺ˝äť?(Type=motion;Act=Start) - äżŽĺ¤ä¸şćŽéĺŻźčŞĺŻĺ?            if (sTypeVal == "MOTION" && sActVal == "START")
            {
                MOOSTrace("ćŁćľĺ°ĺŻźčŞĺŻĺ¨ĺ˝äť¤ (Type=motion;Act=Start)\n");
                // čŽžç˝ŽĺŻźčŞĺŻĺ¨ć ĺż
                m_bNavigationStarted = true;
                MOOSTrace("ĺŻźčŞĺˇ˛ĺŻĺ¨\n");

                // ĺŚćĺ˝ĺćŻčŞĺ¨ć¨Ąĺźďźäźĺ¨ExecuteAutoControlä¸­ĺ¤çä¸ć˛éťčž
                // ĺŚćĺ˝ĺćŻGFć¨Ąĺźďźäźĺ¨ExecuteGFModeä¸­ĺ¤çç¸ĺşéťčž
                MOOSTrace("ĺŻźčŞĺŻĺ¨ďźĺ°ć šćŽĺ˝ĺć¨Ąĺźć§čĄç¸ĺşéťčž\n");
                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                return true;
            }
        }
        else if (sParamName == "ID" && sTypeVal == "CONFIG")
        {
            // ĺ¤çéç˝Žĺ˝äť¤ (Type=CONFIG;ID=MOTOR_POS;VALUE=xxx)
            std::string sIdVal = sOneParam;

            // č§ŁćVALUEĺć°
            sOneParam = MOOSChomp(sStr, ";");
            sParamName = MOOSChomp(sOneParam, "=");
            std::string sValueVal = sOneParam;

            if (sParamName == "VALUE" && sIdVal == "MOTOR_POS")
            {
                MOOSTrace("ćŁćľĺ°çľćşä˝ç˝Žéç˝Žĺ˝äť¤\n");
                double target_angle = atof(sValueVal.c_str());
                MOOSTrace("čŽžç˝ŽçŽć č§ĺşŚ: %.1fĺşŚ\n", target_angle);

                // čŽžç˝ŽçŽć č§ĺşŚĺšśĺŻĺ¨čżĺ?                m_dTargetAngle = target_angle;
                m_bManualAscend = true;
                m_bManualDescend = false;

                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                return true;
            }
            else
            {
                MOOSTrace("éčŻŻďźCONFIGĺ˝äť¤ć źĺźéčŻŻďźID=%sďźVALUEĺć°=%s\n", sIdVal.c_str(), sParamName.c_str());
                return false;
            }
        }

        MOOSTrace("éčŻŻďźćŞčŻĺŤççŽĺć źĺźĺ˝äť? Type=%s;Act=%s\n", sTypeVal.c_str(), sActVal.c_str());
        return false;
    }

    // ĺ¤ć­éŽćŻĺŚä¸şMSGTYPEďźĺ¤ćć źĺźĺ˝äť¤ďź
    if (sParamName != "MSGTYPE")
    {
        MOOSTrace("éčŻŻďźĺ˝äť¤ć źĺźéčŻŻďźçŹŹä¸ä¸Şĺć°ĺşä¸şMSGTYPEćTYPE\n");
        return false;
    }

    // éŽĺźä¸şcontrol
    if (sParamVal == "CONTROL")
    {
        // çŹŹäşć­Ľďźč§ŁćACT
        // ĺĺ˛çŹŹä¸ä¸Şďź
        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is ACT
        // ĺĺ˛çŹŹä¸ä¸?
        sParamName = MOOSChomp(sOneParam, "=");
        // ćżĺ°ĺ?        sParamVal = sOneParam;

        // ĺ¤ć­çŹŹäşä¸Şĺć°ćŻĺŚä¸şACT
        if (sParamName == "ACT")
        {
            // äżĺ­Actçĺ?            std::string sActVal = sParamVal;

            if (sParamVal == "FUNCTION")
            {
                // çŹŹä¸ć­Ľďźč§ŁćTYPE
                sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is TYPE
                sParamName = MOOSChomp(sOneParam, "=");
                std::string sTypeVal = sOneParam;

                if (sParamName == "TYPE")
                {
                    // ć šćŽTYPEçĺźčżčĄä¸ĺçĺ¤ç
                    if (sTypeVal == "MOTION")
                    {
                        // çŹŹĺć­Ľďźč§ŁćModeĺć°
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Mode
                        std::string sModeParam = MOOSChomp(sOneParam, "=");
                        std::string sModeVal = sOneParam;

                        MOOSTrace("č°čŻďźmotionçąťĺĺć°č§Łć - ĺć°ĺ?%s, ĺć°ĺ?%s\n", sModeParam.c_str(), sModeVal.c_str());

                        if (sModeParam == "MODE")
                        {
                            if (sModeVal == "ASCEND")
                            {
                                MOOSTrace("čŽžç˝Žćĺ¨ć§ĺśä¸ĺć ĺż\n");
                                m_bManualAscend = true;
                                m_bManualDescend = false; // çĄŽäżä¸éć ĺżä¸şfalse

                                // čŞĺ¨ĺć˘ĺ°ćĺ¨ć¨ĄĺźďźçŚç¨čŞĺ¨ć§ĺśĺGFć¨Ąĺź
                                if (m_bAutoControl || m_bGFMode)
                                {
                                    MOOSTrace("ćŁćľĺ°ćĺ¨ä¸ĺĺ˝äť¤ďźčŞĺ¨ĺć˘ĺ°ćĺ¨ć¨Ąĺź\n");
                                    m_bAutoControl = false;
                                    m_bGFMode = false;
                                }

                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else if (sModeVal == "DESCEND")
                            {
                                MOOSTrace("čŽžç˝Žćĺ¨ć§ĺśä¸éć ĺż\n");
                                m_bManualDescend = true;
                                m_bManualAscend = false; // çĄŽäżä¸ĺć ĺżä¸şfalse

                                // čŞĺ¨ĺć˘ĺ°ćĺ¨ć¨ĄĺźďźçŚç¨čŞĺ¨ć§ĺśĺGFć¨Ąĺź
                                if (m_bAutoControl || m_bGFMode)
                                {
                                    MOOSTrace("ćŁćľĺ°ćĺ¨ä¸éĺ˝äť¤ďźčŞĺ¨ĺć˘ĺ°ćĺ¨ć¨Ąĺź\n");
                                    m_bAutoControl = false;
                                    m_bGFMode = false;
                                }

                                // ćĺ¨ä¸éĺ˝äť¤ćść čŽ°ĺŻĺ¨ĺťśčżĺˇ˛ć§čĄďźé˛ć­?0ç§čŞĺ¨ćŹčľˇéťčžĺ˛çŞ
                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bStartupDelayExecuted = true;
                                    MOOSTrace("ćśĺ°ćĺ¨ä¸éĺ˝äť¤ďźć čŽ°ĺŻĺ¨ĺťśčżĺˇ˛ć§čĄďźĺć­?0ç§čŞĺ¨ćŹčľˇ\n");
                                }

                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else if (sModeVal == "MOTORSETANGLEMODE")
                            {
                                MOOSTrace("č§ĺşŚć§ĺść¨Ąĺźĺ˝äť¤ĺˇ˛ĺż˝ç?- çłťçťéťčŽ¤ä˝żç¨č§ĺşŚć§ĺść¨Ąĺź\n");
                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("éčŻŻďźćŞçĽçModeçąťĺ: %s\n", sModeVal.c_str());
                            }
                        }
                        else if (sModeParam == "RESET")
                        {
                            if (sModeVal == "YES")
                            {
                                MOOSTrace("ćśĺ°çé˘ć¸éśćäť¤ďźĺéć¸éśĺ˝äť¤\n");
                                // č°ç¨ć¸éśćäť¤ĺ˝ć°
                                SendZeroCommand();
                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("éčŻŻďźResetĺć°ĺşä¸şYESďźĺŽéä¸ş: %s\n", sModeVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("éčŻŻďźmotionçąťĺéčŚModećResetĺć°ďźĺŽéä¸ş: %s\n", sModeParam.c_str());
                        }
                    }
                    else if (sTypeVal == "AUTORISE")
                    {
                        // çŹŹĺć­Ľďźč§ŁćEnableĺć°
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "YES")
                            {
                                MOOSTrace("čŽžç˝ŽčŞĺ¨ć§ĺśĺéć ĺżä¸şĺŻĺ¨\n");
                                m_bAutoControl = true;
                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else if (sEnableVal == "NO")
                            {
                                MOOSTrace("čŽžç˝ŽčŞĺ¨ć§ĺśĺéć ĺżä¸şĺłé­\n");
                                m_bAutoControl = false;
                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("éčŻŻďźEnableĺć°ĺşä¸şYESćNOďźĺŽéä¸ş: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("éčŻŻďźAutoriseçąťĺéčŚEnableĺć°\n");
                        }
                    }
                    else if (sTypeVal == "GF")
                    {
                        // çŹŹĺć­Ľďźč§ŁćEnableĺć°
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "YES")
                            {
                                MOOSTrace("ĺŻĺ¨GFć¨Ąĺźďźĺć­?0ç§čŞĺ¨ĺčľˇ\n");
                                m_bGFMode = true;
                                // ĺŚćć­Łĺ¨ĺĺ¤čŞĺ¨ĺčľˇďźçŤĺłĺć­?                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bManualAscend = false;
                                    m_bManualDescend = false;
                                    MOOSTrace("GFć¨Ąĺźĺˇ˛ĺć­˘čŞĺ¨ĺčľˇ\n");
                                }
                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else if (sEnableVal == "NO")
                            {
                                MOOSTrace("ĺłé­GFć¨Ąĺź\n");
                                m_bGFMode = false;
                                // ćŁćĽćŻĺŚĺŻäťĽĺčľˇďźĺŻĺ¨ĺťśčżĺˇ˛čż ćč?(ĺŻźčŞĺˇ˛ĺŻĺ¨ä¸äťťĺĄĺˇ˛çťć?
                                bool can_ascend = false;

                                // ćĺľ1ďźĺŻĺ¨ĺťśčżćśé´ĺˇ˛čżä¸ćŞć§čĄčżďźĺŞćĺŻç¨CANčŽĄćśĺćĺźĺ§ďź
                                if (!m_bStartupDelayExecuted && m_bCanTimingEnabled && m_dStartupTime > 0)
                                {
                                    double currentTime = MOOSTime();
                                    double elapsedTime = currentTime - m_dStartupTime;
                                    if (elapsedTime >= m_dStartupDelay)
                                    {
                                        can_ascend = true;
                                        MOOSTrace("GFć¨Ąĺźĺłé­ďźĺŻĺ¨ĺťśčżĺˇ˛čżďźäťçŹŹä¸ä¸ŞCANćĽćĺźĺ§čŽĄćśďź\n");
                                    }
                                }

                                // ćĺľ2ďźäťťĺĄçťććĺşćĽçśćä¸ććéĺşŚ=0
                                if ((m_sMissionStatus == "FINISH" || m_sMissionStatus == "EMERGENCY") &&
                                    m_dDesiredSpeed == 0.0)
                                {
                                    if (m_sMissionStatus == "FINISH")
                                    {
                                        // äťťĺĄçťćďźéčŚĺŻźčŞĺˇ˛ĺŻĺ¨ä¸ćˇąĺş?0.5çąłä¸ć ćˇąĺşŚäťťĺ?                                        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
                                        {
                                            can_ascend = true;
                                            MOOSTrace("GFć¨Ąĺźĺłé­ďźäťťĺĄçťćä¸ććéĺşŚ=0ä¸ćĄäťśćťĄčśł\n");
                                        }
                                    }
                                    else if (m_sMissionStatus == "EMERGENCY")
                                    {
                                        // ĺşćĽçśćďźçŤĺłĺčľˇ
                                        can_ascend = true;
                                        MOOSTrace("GFć¨Ąĺźĺłé­ďźĺşćĽçśćä¸ććéĺşŚ=0\n");
                                    }
                                }

                                if (can_ascend)
                                {
                                    MOOSTrace("čŽžç˝ŽčŞĺ¨ĺčľˇ\n");
                                    m_bManualAscend = true;
                                    m_bManualDescend = false;
                                    m_bStartupDelayExecuted = true;
                                }

                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("éčŻŻďźEnableĺć°ĺşä¸şYESćNOďźĺŽéä¸ş: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("éčŻŻďźGFçąťĺéčŚEnableĺć°\n");
                        }
                    }
                    else if (sTypeVal == "STOP")
                    {
                        // çŹŹĺć­Ľďźč§ŁćEnableĺć°
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "NO")
                            {
                                MOOSTrace("ćśĺ°ĺć­˘ĺčľˇĺ˝äť¤ďźĺŻšĺşGFć¨Ąĺź\n");
                                // ćżć´ťGFć¨Ąĺźďźĺć­?0ç§čŞĺ¨ĺčľ?                                m_bGFMode = true;
                                // ĺŚćć­Łĺ¨ĺĺ¤čŞĺ¨ĺčľˇďźçŤĺłĺć­?                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bManualAscend = false;
                                    m_bManualDescend = false;
                                    MOOSTrace("GFć¨Ąĺźĺˇ˛ĺć­˘čŞĺ¨ĺčľˇ\n");
                                }
                                MOOSTrace("=== TypeChoice č§Łććĺ ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("éčŻŻďźSTOPçąťĺçEnableĺć°ĺşä¸şNOďźĺŽéä¸ş: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("éčŻŻďźSTOPçąťĺéčŚEnableĺć°\n");
                        }
                    }
                    else
                    {
                        MOOSTrace("éčŻŻďźćŞçĽçTypeçąťĺ: %s\n", sTypeVal.c_str());
                    }
                }
                else
                {
                    MOOSTrace("éčŻŻďźçŹŹä¸ä¸Şĺć°ĺşä¸şTYPE\n");
                }
            }
            else
            {
                MOOSTrace("éčŻŻďźçŹŹäşä¸Şĺć°ĺşä¸şACT=FUNCTION\n");
            }
        }
        else
        {
            MOOSTrace("éčŻŻďźMsgTypeĺşä¸şCONTROL\n");
        }

        MOOSTrace("=== TypeChoice č§Łćĺ¤ąč´Ľďźĺ˝äť¤ć źĺźä¸ć­ŁçĄŽ ===\n");
        return false;
    }

    // éťčŽ¤čżĺfalseďźä¸ĺşčŻĽĺ°čžžčżéďź?    MOOSTrace("=== TypeChoice č§Łćĺ¤ąč´ĽďźćŞçĽéčŻ?===\n");
    return false;
}
//---------------------------------------------------------
// Procedure: ExecuteAscendControl - ć§čĄä¸ĺć§ĺśďźĺäźçľćşďź?void iLodging_HEU::ExecuteAscendControl()
{
    MOOSTrace("ĺźĺ§ć§čĄĺäźä¸ĺć§ĺś\n");
    // ćĺťşĺäźä¸ĺć§ĺśĺ¸?(13ĺ­čç˝č˝ŹCANć źĺź)
    vector<uint8_t> ascendFrame(FRAME_LEN, 0);

    // CANĺ¸§ĺ¤´é¨ĺ (ĺ­č0-4) - ĺäźçľćşCAN ID = 0x02
    ascendFrame[0] = 0x08; // ĺşĺŽĺ¸§ĺ¤´
    ascendFrame[1] = 0x00; // CAN IDćéŤĺ­č?(0x02 >> 24)
    ascendFrame[2] = 0x00; // CAN IDéŤĺ­č?(0x02 >> 16)
    ascendFrame[3] = 0x00; // CAN IDä˝ĺ­čéŤä˝?(0x02 >> 8)
    ascendFrame[4] = 0x02; // CAN IDä˝ĺ­čä˝ä˝?(0x02 & 0xFF)

    // CANć°ćŽé¨ĺ (ĺ­č5-12) - ćç§ĺäźĺčŽŽć źĺź
    // ĺ­č5: ć§ĺść¨Ąĺź BB=č§ĺşŚć§ĺść¨Ąĺź
    ascendFrame[5] = 0xBB;

    // ĺ­č6: ĺ¤ç¨ĺ­č
    ascendFrame[6] = 0x00;

    // ĺ­č7-8: éĺşŚ (ä˝ç˝ŽçŻć§ĺśćśä¸?)
    MOOSTrace("ĺäźč§ĺşŚć§ĺść¨ĄĺźďźéĺşŚčŽžä¸ş0\n");
    ascendFrame[7] = 0x00; // éĺşŚéŤä˝
    ascendFrame[8] = 0x00; // éĺşŚä˝ä˝

    // ĺ­č9-10: ććĺşŚć° - ĺć­ť90ĺşŚä¸ĺĺ˝äť?    // 90ĺşŚä¸ĺĺşĺŽĺ˝äť¤ďź0x7FFF
    ascendFrame[9] = 0x7F; // ććĺşŚć°éŤä˝
    ascendFrame[10] = 0xFF; // ććĺşŚć°ä˝ä˝

    // ĺ­č11-12: ĺ¤ç¨ĺ­č
    ascendFrame[11] = 0x00;
    ascendFrame[12] = 0x00;

    SendFrame(ascendFrame);
    MOOSTrace("ĺäźä¸ĺć§ĺśĺ¸§ĺˇ˛ĺé?CAN ID=0x02): ć¨Ąĺź=0x%02X, çŽć č§ĺşŚ=90Â°, ĺčŽŽĺ?0x7FFF\n",
              ascendFrame[5]);
}

//---------------------------------------------------------
// Procedure: ExecuteDescendControl - ć§čĄä¸éć§ĺśďźĺäźçľćşďź?void iLodging_HEU::ExecuteDescendControl()
{
    MOOSTrace("ĺźĺ§ć§čĄĺäźä¸éć§ĺś\n");

    // ćĺťşĺäźä¸éć§ĺśĺ¸?(13ĺ­čç˝č˝ŹCANć źĺź)
    vector<uint8_t> descendFrame(FRAME_LEN, 0);

    // CANĺ¸§ĺ¤´é¨ĺ (ĺ­č0-4) - ĺäźçľćşCAN ID = 0x02
    descendFrame[0] = 0x08; // ĺşĺŽĺ¸§ĺ¤´
    descendFrame[1] = 0x00; // CAN IDćéŤĺ­č?(0x02 >> 24)
    descendFrame[2] = 0x00; // CAN IDéŤĺ­č?(0x02 >> 16)
    descendFrame[3] = 0x00; // CAN IDä˝ĺ­čéŤä˝?(0x02 >> 8)
    descendFrame[4] = 0x02; // CAN IDä˝ĺ­čä˝ä˝?(0x02 & 0xFF)

    // CANć°ćŽé¨ĺ (ĺ­č5-12) - ćç§ĺäźĺčŽŽć źĺź
    // ĺ­č5: ć§ĺść¨Ąĺź BB=č§ĺşŚć§ĺść¨Ąĺź
    descendFrame[5] = 0xBB;

    // ĺ­č6: ĺ¤ç¨ĺ­č
    descendFrame[6] = 0x00;

    // ĺ­č7-8: éĺşŚ (ä˝ç˝ŽçŻć§ĺśćśä¸?)
    MOOSTrace("ĺäźč§ĺşŚć§ĺść¨ĄĺźďźéĺşŚčŽžä¸ş0\n");
    descendFrame[7] = 0x00; // éĺşŚéŤä˝
    descendFrame[8] = 0x00; // éĺşŚä˝ä˝

    // ĺ­č9-10: ććĺşŚć° - ĺć­ť0ĺşŚä¸éĺ˝äť?    // 0ĺşŚä¸éĺşĺŽĺ˝äť¤ďź0x4000
    descendFrame[9] = 0x40; // ććĺşŚć°éŤä˝
    descendFrame[10] = 0x00; // ććĺşŚć°ä˝ä˝

    // ĺ­č11-12: ĺ¤ç¨ĺ­č
    descendFrame[11] = 0x00;
    descendFrame[12] = 0x00;

    SendFrame(descendFrame);
    MOOSTrace("ĺäźä¸éć§ĺśĺ¸§ĺˇ˛ĺé?CAN ID=0x02): ć¨Ąĺź=0x%02X, çŽć č§ĺşŚ=0Â°, ĺčŽŽĺ?0x4000\n",
              descendFrame[5]);
}

//---------------------------------------------------------
// Procedure: ExecuteAutoControl - ć§čĄčŞĺ¨ć§ĺś
void iLodging_HEU::ExecuteAutoControl()
{
    // čŞĺ¨ć§ĺśéťčžďźĺ¤çĺŻźčŞĺäťťĺĄç¸ĺłçčŞĺ¨ĺéĺşć?
    // 1. ćŁćĽĺŻčŞćĄäťśďźĺŻźčŞĺŻĺ¨ĺä¸ć˛?    if (m_bNavigationStarted && !m_bHasDescendedAfterNav)
    {
        MOOSTrace("ćŁćľĺ°ĺŻźčŞĺˇ˛ĺŻĺ¨ďźć§čĄä¸ć˛ćä˝\n");
        m_bManualDescend = true;
        m_bManualAscend = false;
        m_bHasDescendedAfterNav = true;

        // ĺŻźčŞĺŻĺ¨ä¸ć˛ćść čŽ°ĺŻĺ¨ĺťśčżĺˇ˛ć§čĄďźçłťçťčżĺĽć­Łĺ¸¸äťťĺĄćľç¨?        if (!m_bStartupDelayExecuted)
        {
            m_bStartupDelayExecuted = true;
            MOOSTrace("ĺŻźčŞĺŻĺ¨ä¸ć˛ďźć čŽ°ĺŻĺ¨ĺťśčżĺˇ˛ć§čĄďźçłťçťčżĺĽć­Łĺ¸¸äťťĺĄćľç¨\n");
        }
    }

    // 2. ćŁćĽäťťĺĄçťććĄäťśďźCtrlMission_STATUS = "FINISH" ćśä¸ĺďźé¤éGFć¨ĄĺźćéŤéć¨Ąĺźďź
    MOOSTrace("čŞĺ¨ć§ĺśč°čŻďźäťťĺĄçść?%s, GFć¨Ąĺź=%s, éŤéć¨Ąĺź?%s\n",
              m_sMissionStatus.c_str(), m_bGFMode ? "ć? : "ĺ?, IsHighSpeedMode() ? "ć? : "ĺ?);

    if (m_sMissionStatus == "FINISH" && !m_bGFMode && !IsHighSpeedMode())
    {
        MOOSTrace("čŞĺ¨ć§ĺśďźčżĺĽäťťĺĄçťććŁćĽéťčž\n");

        // ćŁćĽä¸ĺćĄäťśďźäťťĺĄçťćä¸ććéĺşŚä¸?ĺłĺŻčŞĺ¨ä¸ĺ
        MOOSTrace("čŞĺ¨ć§ĺśďźćŁćĽä¸ĺćĄäť?- ććéĺşŚ=%.1f\n", m_dDesiredSpeed);
        // äťťĺĄçťćä¸ććéĺşŚä¸?
        if (m_dDesiredSpeed == 0.0)
        {
            MOOSTrace("čŞĺ¨ć§ĺśďźććéĺşŚćĄäťśćťĄčśłďźćŁćĽć ĺżä˝ - ĺˇ˛ä¸ĺ?%s, ćĺ¨ä¸ĺ=%s, ćĺ¨ä¸é=%s\n",
                      m_bHasAscendedAfterFinish ? "ć? : "ĺ?,
                      m_bManualAscend ? "ć? : "ĺ?,
                      m_bManualDescend ? "ć? : "ĺ?);

            // ç§ťé¤ m_bHasAscendedAfterFinish ćŁćĽďźĺčŽ¸ćŻćŹĄFINISHé˝č§Śĺä¸ĺ?            if (!m_bManualAscend && !m_bManualDescend)
            {
                MOOSTrace("äťťĺĄçťćďźććéĺşŚ=%.1fďźć§čĄčŞĺ¨ä¸ĺďźĺčŽ¸éĺ¤č§Śĺďź\n", m_dDesiredSpeed);
                m_bManualAscend = true;
                m_bManualDescend = false;
                // ćł¨éćďźä¸ĺčŽžç˝Žĺˇ˛ä¸ĺć ĺżďźĺčŽ¸éĺ¤č§Śĺ
                // m_bHasAscendedAfterFinish = true;
            }
            else
            {
                MOOSTrace("čŞĺ¨ć§ĺśďźĺ˝ĺć­Łĺ¨čżĺ¨ďźčˇłčżä¸ĺćäť¤\n");
            }
        }
        else
        {
            MOOSTrace("čŞĺ¨ć§ĺśďźććéĺşŚćĄäťśä¸ćťĄčśł\n");
        }
    }
    else
    {
        MOOSTrace("čŞĺ¨ć§ĺśďźä¸ćťĄčśłäťťĺĄçťćä¸ĺçĺşćŹćĄäťś\n");
    }
    if (m_sMissionStatus == "FINISH" && IsHighSpeedMode())
    {
        MOOSTrace("äťťĺĄçťćďźä˝ćŁćľĺ°éŤéć¨Ąĺźďźä¸ć§čĄčŞĺ¨ä¸ĺ\n");
    }

    // 4. ćŁćĽĺşćĽçśćďźçŤĺłä¸ĺďźć ééĺşŚćĄäťśďź?    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("čŞĺ¨ć§ĺśďźćŁćľĺ°ĺşćĽçśćďźććéĺşŚ=%.1f\n", m_dDesiredSpeed);

        // ćŁćĽäťťĺĄçśććŻĺŚĺĺĺä¸şEMERGENCYďźéç˝Žć ĺżďź
        if (m_sLastMissionStatus != "EMERGENCY")
        {
            m_bHasAscendedInEmergency = false;
            MOOSTrace("ĺşćĽçśćĺĺĺźĺ§ďźéç˝Žä¸ĺć ĺż\n");
        }

        if (!m_bHasAscendedInEmergency && !m_bManualAscend && !m_bManualDescend)
        {
            MOOSTrace("ćŁćľĺ°ĺşćĽçśćďźçŤĺłć§čĄä¸ĺďźć č§éĺşŚćĄäťśďź\n");
            m_bManualAscend = true;
            m_bManualDescend = false;
            m_bHasAscendedInEmergency = true;
        }
        else
        {
            MOOSTrace("ĺşćĽçśćďźĺˇ˛ä¸ĺ?%s, ćĺ¨ä¸ĺ=%s, ćĺ¨ä¸é=%s\n",
                      m_bHasAscendedInEmergency ? "ć? : "ĺ?,
                      m_bManualAscend ? "ć? : "ĺ?,
                      m_bManualDescend ? "ć? : "ĺ?);
        }
    }

    // 5. ĺşäşéĺşŚçčŞĺ¨ć§ĺśďźć šćŽDESIRED_SPEEDĺłĺŽĺé
    // ĺşćĽçśćä¸čˇłčżéĺşŚć§ĺśďźç´ćĽä¸ĺ?    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("čŞĺ¨ć§ĺśďźĺşćĽçśćďźčˇłčżéĺşŚć§ĺśéťčž\n");
    }
    else
    {
        // 60ç§ĺčŞĺ¨ć¨Ąĺźďźć šćŽčŞčĄéĺşŚĺłĺŽĺčľˇ/ä¸é
        MOOSTrace("čŞĺ¨ć§ĺśďźéĺşŚć§ĺś - ććéĺşŚ=%.0f mm/s, éŤééĺ?%.0f mm/s\n",
                  m_dDesiredSpeed, m_dHighSpeed);

        // éżĺé˘çšĺć˘ďźĺŞćĺ¨ć˛Ąććĺ¨ć§ĺśćśćć§čĄéĺşŚć§ĺś
        if (!m_bManualAscend && !m_bManualDescend)
    {
        // ććçéĺşŚĺ°äşćéŤéďźĺ°ąĺčľˇćĽ
        if (m_dDesiredSpeed < m_dHighSpeed && !m_bAtTop)
        {
            MOOSTrace("čŞĺ¨ć§ĺśďźä˝é?%.0f < %.0f)ďźć§čĄä¸ĺ\n",
                      m_dDesiredSpeed, m_dHighSpeed);
            m_bManualAscend = true;
            m_bManualDescend = false;
        }
        // éŤéćśä¸éďźĺ˘ĺ ç¨łĺŽć§ďź
        else if (m_dDesiredSpeed >= m_dHighSpeed && !m_bAtBottom)
        {
            MOOSTrace("čŞĺ¨ć§ĺśďźéŤé?%.0f >= %.0f)ďźć§čĄä¸é\n",
                      m_dDesiredSpeed, m_dHighSpeed);
            m_bManualDescend = true;
            m_bManualAscend = false;
        }
        else
        {
            MOOSTrace("čŞĺ¨ć§ĺśďźĺˇ˛ĺ¨ĺéä˝ç˝Žćĺ°čžžćéä˝ç˝Ž\n");
        }
    }
        else
        {
            MOOSTrace("čŞĺ¨ć§ĺśďźĺ˝ĺććĺ¨ć§ĺśćäť¤ďźčˇłčżéĺşŚć§ĺś\n");
        }
    }

    // ć´ć°ä¸ćŹĄäťťĺĄçść?    m_sLastMissionStatus = m_sMissionStatus;
}

//---------------------------------------------------------
// Procedure: ExecuteGFMode - ć§čĄGFć¨Ąĺź
void iLodging_HEU::ExecuteGFMode()
{
    // ĺžĺŻčŞĺä¸äťťĺĄçťćďźćˇąĺşŚć°ćŽďź?.5çąłďź60sĺčľˇćĺäšĺďź?    // éĺşGFć¨Ąĺźĺć˘ĺ°čŞĺ¨ć¨ĄĺźďźĺŚćĺčľˇĺ¤ąč´Ľďźĺéä¸éćäť¤ďźäżćGFć¨Ąĺź
    MOOSTrace("ć§čĄGFć¨Ąĺźéťčž\n");

    // GFć¨Ąĺźçć ¸ĺżéťčžďźéťć­˘čľćşä¸ĺďźç´ĺ°ćśĺ°äťťĺĄçťććĺŻčŞĺ˝äť?
    // 1. éťć­˘ććčŞĺ¨ä¸ĺćä˝?    if (m_bManualAscend)
    {
        MOOSTrace("GFć¨Ąĺźćżć´ťďźéťć­˘ä¸ĺćä˝\n");
        m_bManualAscend = false; // ĺćśä¸ĺć ĺż
    }

    // 2. GFć¨Ąĺźä¸ťĺ¨ä¸ééťčžďźçĄŽäżçľć˘Żéĺ°ĺşé¨ďźä˝čŚćŁćĽćŻĺŚĺşčŻĽä¸ĺďź
    // éŚĺćŁćĽćŻĺŚćťĄčśłä¸ĺćĄäť?    bool should_ascend_now = false;

    // ćŁćĽäťťĺĄçťćä¸ĺćĄäť?    if (m_sMissionStatus == "FINISH" && m_dDesiredSpeed == 0.0 && !IsHighSpeedMode())
    {
        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
        {
            should_ascend_now = true;
        }
    }

    // ćŁćĽĺşćĽçśćä¸ĺćĄäťśďźć ééĺşŚćĄäťśďź?    if (m_sMissionStatus == "EMERGENCY")
    {
        should_ascend_now = true;
        MOOSTrace("GFć¨ĄĺźďźćŁćľĺ°ĺşćĽçśćďźć č§éĺşŚćĄäťśďźĺĺ¤ä¸ĺ\n");
    }

    // ĺŞćĺ¨ä¸éčŚä¸ĺćśćć§čĄä¸ééťčž
    if (!m_bAtBottom && !m_bManualDescend && !should_ascend_now)
    {
        MOOSTrace("GFć¨Ąĺźćżć´ťďźGFćŞĺ¨ĺşé¨ä¸ć ä¸ĺéćąďźć§čĄä¸éĺ°ĺşé¨\n");
        m_bManualDescend = true; // čŽžç˝Žä¸éć ĺż
        m_bManualAscend = false; // çĄŽäżä¸ĺć ĺżä¸şfalse
    }
    else if (should_ascend_now)
    {
        MOOSTrace("GFć¨ĄĺźďźćŁćľĺ°ä¸ĺćĄäťśďźčˇłčżä¸ééťčž\n");
    }

    // 3. ćŁćĽćŻĺŚĺşčŻĽéĺşGFć¨ĄĺźĺšśĺčŽ¸ä¸ĺ?    bool should_exit_gf_and_ascend = false;

    // ćĄäťś1ďźäťťĺĄçťćä¸ćťĄčśłä¸ĺćĄäťśďźä˝ä¸č˝ćŻéŤéć¨Ąĺźďź
    if (m_sMissionStatus == "FINISH" && m_dDesiredSpeed == 0.0 && !IsHighSpeedMode())
    {
        MOOSTrace("GFć¨ĄĺźďźćŁćĽä¸ĺćĄäť?- äťťĺĄçść?FINISH, ććéĺşŚ=%.1f, éŤéć¨Ąĺź?%s\n",
                  m_dDesiredSpeed, IsHighSpeedMode() ? "ć? : "ĺ?);

        // äťťĺĄçťćďźéčŚĺŻźčŞĺˇ˛ĺŻĺ¨ä¸ćˇąĺş?0.5çąłä¸ć ćˇąĺşŚäťťĺ?        MOOSTrace("GFć¨ĄĺźďźčŻŚçťćĄäťśćŁć?- ĺŻźčŞĺŻĺ¨=%s, ĺ˝ĺćˇąĺşŚ=%.2f, ćććˇąĺşŚ=%.2f\n",
                  m_bNavigationStarted ? "ć? : "ĺ?, m_dCurrentDepth, m_dDesiredDepth);

        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
        {
            should_exit_gf_and_ascend = true;
            MOOSTrace("GFć¨ĄĺźďźćććĄäťśćťĄčśłďźĺĺ¤ä¸ĺ\n");
        }
        else
        {
            MOOSTrace("GFć¨ĄĺźďźćĄäťśä¸ćťĄčśłďźçť§çť­ç­ĺž\n");
        }
    }
    else if (m_sMissionStatus == "FINISH" && IsHighSpeedMode())
    {
        MOOSTrace("GFć¨ĄĺźďźäťťĺĄçťćä˝ćŁćľĺ°éŤéć¨Ąĺźďźçť§çť­éťć­˘ä¸ĺ\n");
    }

    // ćĄäťś4ďźĺşćĽçść?- çŤĺłä¸ĺďźć ééĺşŚćĄäťśďź?    if (m_sMissionStatus == "EMERGENCY")
    {
        // ĺşćĽçśćä¸çŤĺłä¸ĺďźçĄŽäżĺŽĺ¨ďźć č§éĺşŚćĄäťś
        MOOSTrace("GFć¨ĄĺźďźćŁćľĺ°ĺşćĽçśćďźçŤĺłĺčŽ¸ä¸ĺďźć č§éĺşŚ=%.1fďź\n", m_dDesiredSpeed);
        should_exit_gf_and_ascend = true; // ĺşćĽçśćçŤĺłä¸ĺ?    }

    // ćĄäťś5ďźĺŻčŞĺ˝äť¤ďźĺŻźčŞĺŻĺ¨ďź?    if (m_bNavigationStarted && m_sMissionStatus != "FINISH")
    {
        // ĺŞćĺ¨äťťĺĄćŞçťććśďźĺŻčŞćĺşčŻĽä¸é?        MOOSTrace("GFć¨ĄĺźďźćŁćľĺ°ĺŻčŞĺ˝äť¤ďźäťťĺĄćŞçťćďźĺşä¸é\n");
        should_exit_gf_and_ascend = false; // éťć­˘ä¸ĺ
    }

    // ĺŚććťĄčśłéĺşGFć¨Ąĺźĺšśä¸ĺçćĄäťś
    MOOSTrace("GFć¨Ąĺźďźshould_exit_gf_and_ascend = %s\n", should_exit_gf_and_ascend ? "true" : "false");
    if (should_exit_gf_and_ascend)
    {
        // ĺŚćčżć˛Ąćĺźĺ§ĺčľˇćä˝ďźĺĺŻĺ¨ĺčľˇĺšśçŤĺłĺć˘ĺ°čŞĺ¨ć¨Ąĺź?        if (!m_bAscendInProgress)
        {
            MOOSTrace("GFć¨ĄĺźďźćĄäťśćťĄčśłďźĺźĺ§ć§čĄä¸ĺćä˝ĺšśĺć˘ĺ°čŞĺ¨ć¨Ąĺź\n");

            // çŤĺłéĺşGFć¨Ąĺźďźĺć˘ĺ°čŞĺ¨ć¨Ąĺź
            m_bGFMode = false;
            m_bAutoControl = true;

            // čŽžç˝Žä¸ĺć ĺż
            m_bManualAscend = true;     // čŽžç˝Žä¸ĺć ĺż
            m_bManualDescend = false;   // ć¸é¤ä¸éć ĺż
            m_bAscendInProgress = true; // ć čŽ°ĺčľˇćä˝ĺźĺ§?
            // ć čŽ°ĺŻĺ¨ĺťśčżĺˇ˛ć§čĄďźéżĺéĺ¤č§Śĺďź?            if (!m_bStartupDelayExecuted)
            {
                m_bStartupDelayExecuted = true;
            }

            MOOSTrace("GFć¨Ąĺźďźĺˇ˛éĺşGFć¨Ąĺźďźĺć˘ĺ°čŞĺ¨ć§ĺść¨Ąĺź\n");
        }
        else
        {
            MOOSTrace("GFć¨Ąĺźďźä¸ĺćä˝ĺˇ˛ĺ¨čżčĄä¸­ďźm_bAscendInProgress = true\n");
        }
        // ĺčľˇćä˝ć­Łĺ¨čżčĄä¸­ďźĺčľˇćĺĺĺ¤ąč´ĽçćŁćľĺ¨ParseFrameä¸­ĺ¤ç?    }
    else
    {
        MOOSTrace("GFć¨Ąĺźďźçť§çť­éťć­˘ä¸ĺďźç­ĺžäťťĺĄçťććĺŻčŞĺ˝äť¤\n");
    }

    MOOSTrace("GFć¨Ąĺźéťčžć§čĄĺŽć\n");
}

//---------------------------------------------------------
// Procedure: SendHoldPositionCommand - ĺéäżćä˝ç˝Žĺ˝äť?void iLodging_HEU::SendHoldPositionCommand(uint8_t current_rounds, uint16_t current_degrees, uint8_t motor_id)
{
    // ćĺťşäżćä˝ç˝Žć§ĺśĺ¸§ďźä˝żç¨ĺ˝ĺĺć°ĺč§ĺşŚä˝ä¸şçŽć ä˝ç˝?    vector<uint8_t> holdFrame(FRAME_LEN, 0);

    // CANĺ¸§ĺ¤´é¨ĺ (ĺ­č0-4) - ä˝żç¨ćĺŽççľćşCAN ID
    holdFrame[0] = 0x08;     // ĺşĺŽĺ¸§ĺ¤´
    holdFrame[1] = 0x00;     // CAN IDćéŤĺ­č?(motor_id >> 24)
    holdFrame[2] = 0x00;     // CAN IDéŤĺ­č?(motor_id >> 16)
    holdFrame[3] = 0x00;     // CAN IDä˝ĺ­čéŤä˝?(motor_id >> 8)
    holdFrame[4] = motor_id; // CAN IDä˝ĺ­čä˝ä˝?(motor_id & 0xFF)

    // CANć°ćŽé¨ĺ (ĺ­č5-12) - ĺäźĺčŽŽć źĺź
    holdFrame[5] = 0xBB;                           // ć§ĺść¨Ąĺź = č§ĺşŚć§ĺś
    holdFrame[6] = 0x00;                           // ĺ¤ç¨ĺ­č
    holdFrame[7] = 0x00;                           // éĺşŚéŤä˝ (ä˝ç˝ŽçŻć§ĺśćśä¸?)
    holdFrame[8] = 0x00;                           // éĺşŚä˝ä˝ (ä˝ç˝ŽçŻć§ĺśćśä¸?)

    // ç´ćĽä˝żç¨ĺşŚć°ĺźč˝Źć?    uint16_t fall_value = ConvertAngleToFallValue((double)current_degrees);

    holdFrame[9] = (fall_value >> 8) & 0xFF;       // ććĺşŚć°éŤä˝
    holdFrame[10] = fall_value & 0xFF;             // ććĺşŚć°ä˝ä˝
    holdFrame[11] = 0x00;                          // ĺ¤ç¨ĺ­č
    holdFrame[12] = 0x00;                          // ĺ¤ç¨ĺ­č

    SendFrame(holdFrame);
    MOOSTrace("ĺäźäżćä˝ç˝Žć§ĺśĺ¸§ĺˇ˛ĺé?CAN ID=0x%02X): ć¨Ąĺź=0x%02X, çŽć č§ĺşŚ=%.1fÂ°, ĺčŽŽĺ?0x%04X\n",
              motor_id, holdFrame[5], (double)current_degrees, fall_value);
}

//---------------------------------------------------------
// Procedure: SendZeroCommand - ĺéć¸éśćäť?void iLodging_HEU::SendZeroCommand()
{
    // ćĺťşć¸éśćäť¤ĺ¸§ďźä˝żç¨CAN ID 0x7FF
    vector<uint8_t> zeroFrame(FRAME_LEN, 0);

    // CANĺ¸§ĺ¤´é¨ĺ (ĺ­č0-4) - ć¸éśćäť¤CAN ID = 0x7FF
    zeroFrame[0] = 0x08; // ĺşĺŽĺ¸§ĺ¤´
    zeroFrame[1] = 0x00; // CAN IDćéŤĺ­č?(0x7FF >> 24)
    zeroFrame[2] = 0x00; // CAN IDéŤĺ­č?(0x7FF >> 16)
    zeroFrame[3] = 0x07; // CAN IDä˝ĺ­čéŤä˝?(0x7FF >> 8)
    zeroFrame[4] = 0xFF; // CAN IDä˝ĺ­čä˝ä˝?(0x7FF & 0xFF)

    // CANć°ćŽé¨ĺ (ĺ­č5-12) - ć šćŽĺčŽŽććĄŁčŽžç˝Žć¸éśćäť¤
    zeroFrame[5] = 0x00;  // čľćşIDĺ?    zeroFrame[6] = 0x01;  // ć§ĺśĺ¨ä¸ĺ?    zeroFrame[7] = 0x00;  // äżçĺ­č
    zeroFrame[8] = 0x03;  // ć¸éśĺ˝äť¤ç ?    zeroFrame[9] = 0x00;  // äżçĺ­č
    zeroFrame[10] = 0x00; // äżçĺ­č
    zeroFrame[11] = 0x00; // äżçĺ­č
    zeroFrame[12] = 0x00; // äżçĺ­č

    SendFrame(zeroFrame);
    MOOSTrace("ć¸éśćäť¤ĺˇ˛ĺé?CAN ID=0x7FF): čľćşID=0x%02X, ć§ĺśç ?0x%02X, ĺ˝äť¤=0x%02X\n",
              zeroFrame[5], zeroFrame[6], zeroFrame[8]);
}

//---------------------------------------------------------
// Procedure: SendPositionCommand - ĺéä˝ç˝Žć§ĺśćäť¤ďźĺäźĺčŽŽďź?void iLodging_HEU::SendPositionCommand(uint8_t target_rounds, uint16_t target_degrees, uint8_t motor_id)
{
    // ćĺťşĺäźä˝ç˝Žć§ĺśćäť¤ĺ¸?    vector<uint8_t> positionFrame(FRAME_LEN, 0);

    // CANĺ¸§ĺ¤´é¨ĺ (ĺ­č0-4) - ä˝żç¨ćĺŽççľćşID
    positionFrame[0] = 0x08;     // ĺşĺŽĺ¸§ĺ¤´
    positionFrame[1] = 0x00;     // CAN IDćéŤĺ­č?    positionFrame[2] = 0x00;     // CAN IDéŤĺ­č?    positionFrame[3] = 0x00;     // CAN IDä˝ĺ­čéŤä˝?    positionFrame[4] = motor_id; // CAN IDä˝ĺ­čä˝ä˝?
    // CANć°ćŽé¨ĺ (ĺ­č5-12) - ĺäźĺčŽŽä˝ç˝Žć§ĺśćäť¤
    positionFrame[5] = 0xBB;                          // č§ĺşŚć§ĺść¨Ąĺź
    positionFrame[6] = 0x00;                          // ĺ¤ç¨ĺ­č
    positionFrame[7] = 0x00;                          // éĺşŚéŤä˝ďźä˝ç˝ŽçŻć§ĺśćśä¸ş0ďź?    positionFrame[8] = 0x00;                          // éĺşŚä˝ä˝

    // ĺäźĺčŽŽďźĺż˝çĽĺć°ďźç´ćĽä˝żç¨ĺşŚć°ĺźč˝Źć˘ä¸şĺäźĺčŽŽĺ?    // target_roundsĺ¨ĺäźĺčŽŽä¸­ć ćäšďźç´ćĽä˝żç¨target_degrees
    uint16_t fall_value = ConvertAngleToFallValue((double)target_degrees);

    positionFrame[9] = (fall_value >> 8) & 0xFF;      // ććĺşŚć°éŤä˝
    positionFrame[10] = fall_value & 0xFF;            // ććĺşŚć°ä˝ä˝
    positionFrame[11] = 0x00;                         // ĺ¤ç¨ĺ­č
    positionFrame[12] = 0x00;                         // ĺ¤ç¨ĺ­č

    SendFrame(positionFrame);
    MOOSTrace("ĺäźä˝ç˝Žć§ĺśćäť¤ĺˇ˛ĺé?CAN ID=0x%02X): ć¨Ąĺź=0x%02X, çŽć č§ĺşŚ=%.1fÂ°, ĺčŽŽĺ?0x%04X\n",
              motor_id, positionFrame[5], (double)target_degrees, fall_value);
}

/*
//---------------------------------------------------------
// Procedure: ConvertAngleToComponents - ĺ°ćťč§ĺşŚč˝Źć˘ä¸şĺć°+ĺşŚć°ć źĺź
// ćł¨éďźĺäźĺčŽŽä¸éčŚć­¤ĺ˝ć°ďźĺ ä¸şć˛Ąćĺć°ćŚĺżľďźč§ĺşŚčĺ´äť?Â°-90Â°
// äżçć­¤ĺ˝ć°äťĽé˛ĺźĺŽšć§éčŚďźä˝ĺäźć§ĺśä¸­ä¸ĺşä˝żç?AngleComponents iLodging_HEU::ConvertAngleToComponents(double total_angle)
{
    AngleComponents result;

    // çĄŽäżč§ĺşŚä¸şć­Łĺ?    if (total_angle < 0)
    {
        total_angle = 0;
        MOOSTrace("č­Śĺďźč§ĺşŚĺźä¸şč´ďźĺˇ˛čŽžä¸?\n");
    }

    // čŽĄçŽĺŽć´ĺć°
    result.rounds = (uint8_t)(total_angle / 360.0);

    // čŽĄçŽĺŠä˝ĺşŚć°
    double remaining_degrees = total_angle - (result.rounds * 360.0);
    result.degrees = (uint16_t)remaining_degrees;

    // çĄŽäżĺşŚć°ĺ?-359čĺ´ĺ?    if (result.degrees >= 360)
    {
        result.rounds += result.degrees / 360;
        result.degrees = result.degrees % 360;
    }
    // čżčžšĺŚććŻéŤéçśćä¸çčŻĺ°ąčŽŠäťčŞĺˇąĺ˝é?    MOOSTrace("č§ĺşŚč˝Źć˘: %.1fÂ° -> %dĺ?+ %dĺşŚ\n",
              total_angle, result.rounds, result.degrees);

    return result;
}
*/

//---------------------------------------------------------
// Procedure: ConvertAngleToFallValue - ĺ°č§ĺşŚč˝Źć˘ä¸şĺäźĺčŽŽĺ?uint16_t iLodging_HEU::ConvertAngleToFallValue(double angle)
{
    // ćŠĺąč§ĺşŚčĺ´äťĽćŻćĺžŽč°ĺč˝ďź-10Â°ĺ?00Â°
    if (angle < -10.0)
    {
        angle = -10.0;
        MOOSTrace("č­Śĺďźĺäźč§ĺşŚĺźĺ°äş?10Â°ďźĺˇ˛čŽžä¸ş-10Â°\n");
    }
    else if (angle > 100.0)
    {
        angle = 100.0;
        MOOSTrace("č­Śĺďźĺäźč§ĺşŚĺźčśčż?00Â°ďźĺˇ˛čŽžä¸ş100Â°\n");
    }

    // ć šćŽĺäźĺčŽŽďź?Â°ĺŻšĺş0x7FFFďź?0Â°ĺŻšĺş0x4000ďźĺĺć ĺ°ďź
    // ćŠĺąćŻć-10Â°ĺ?00Â°čĺ´ďź?    // -10Â°ĺŻšĺşčśčż0x7FFFçĺźďźä˝żç¨0x7FFFďź?    // 100Â°ĺŻšĺşĺ°äş0x4000çĺźďźä˝żç¨0x4000ďź?
    // ĺ°č§ĺşŚć ĺ°ĺ°0-90čĺ´çćŻäž?    double normalized_ratio;
    if (angle < 0.0) {
        // č´č§ĺşŚďź0Â°ĺŻšĺş0x7FFFďź?10Â°äšĺŻšĺş?x7FFFďźćŞć­ďź
        normalized_ratio = 0.0;
    } else if (angle > 90.0) {
        // čśčż90Â°ďź?0Â°ĺŻšĺş0x4000ďź?00Â°äšĺŻšĺş?x4000ďźćŞć­ďź
        normalized_ratio = 1.0;
    } else {
        // 0Â°-90Â°čĺ´ĺďźçşżć§ć ĺ°?        normalized_ratio = angle / 90.0;
    }

    uint16_t fall_value = 0x7FFF - (uint16_t)(normalized_ratio * (0x7FFF - 0x4000));

    MOOSTrace("ĺäźč§ĺşŚč˝Źć˘: %.1fÂ° -> 0x%04X\n", angle, fall_value);

    return fall_value;
}

//---------------------------------------------------------
// Procedure: ConvertFallValueToAngle - ĺ°ĺäźĺčŽŽĺźč˝Źć˘ä¸şč§ĺşŚ
double iLodging_HEU::ConvertFallValueToAngle(uint16_t fall_value)
{
    // ćŠĺąĺčŽŽĺźčĺ´äťĽćŻćĺžŽč°ĺč˝
    if (fall_value < 0x4000)
    {
        fall_value = 0x4000;
        MOOSTrace("č­ŚĺďźĺäźĺčŽŽĺźĺ°äş?x4000ďźĺˇ˛čŽžä¸ş0x4000ďźĺŻšĺş?00Â°ďź\n");
    }
    else if (fall_value > 0x7FFF)
    {
        fall_value = 0x7FFF;
        MOOSTrace("č­ŚĺďźĺäźĺčŽŽĺźĺ¤§äş?x7FFFďźĺˇ˛čŽžä¸ş0x7FFFďźĺŻšĺş?10Â°ďź\n");
    }

    // ć šćŽĺäźĺčŽŽďź?Â°ĺŻšĺş0x7FFFďź?0Â°ĺŻšĺş0x4000ďźĺĺć ĺ°ďź
    // čŽĄçŽĺŹĺźďźangle = ((0x7FFF - fall_value) / (0x7FFF - 0x4000)) * 90.0
    double angle = ((double)(0x7FFF - fall_value) / (double)(0x7FFF - 0x4000)) * 90.0;

    MOOSTrace("ĺäźĺčŽŽĺźč˝Źć? 0x%04X -> %.1fÂ°\n", fall_value, angle);

    return angle;
}

//---------------------------------------------------------
// Procedure: ConvertComponentsToAngle - ĺ°ĺć?ĺşŚć°č˝Źć˘ä¸şćťč§ĺş?// ćł¨éďźĺäźĺčŽŽä¸éčŚć­¤ĺ˝ć°ďźĺ ä¸şć˛Ąćĺć°ćŚĺżľďźč§ĺşŚčĺ´äť?Â°-90Â°
// äżçć­¤ĺ˝ć°äťĽé˛ĺźĺŽšć§éčŚďźä˝ĺäźć§ĺśä¸­ĺşéżĺä˝żç¨
double iLodging_HEU::ConvertComponentsToAngle(uint8_t rounds, uint16_t degrees)
{
    // çĄŽäżĺşŚć°ĺ¨ĺçčĺ´ĺ
    if (degrees >= 360)
    {
        MOOSTrace("č­ŚĺďźĺşŚć°ĺ?dčśčż359ďźčżčĄäżŽć­Ł\n", degrees);
        // čżčžšćŻä¸şäťäščŚčżäšĺçĺĺ 
        // rounds += degrees/360 â?ĺ˘ĺ ĺŽć´ĺć°
        // degrees = degrees%360 â?äżçĺŠä˝ĺşŚć°
        rounds += degrees / 360;
        degrees = degrees % 360;
    }
    // ćťč§ĺşŚç­äşĺć?ĺşŚć°
    double total_angle = rounds * 360.0 + degrees;

    MOOSTrace("č§ĺşŚĺć: %dĺ?+ %dĺş?-> %.1fÂ°\n",
              rounds, degrees, total_angle);

    return total_angle;
}

//---------------------------------------------------------
// Procedure: IsHighSpeedMode - ĺ¤ć­ćŻĺŚä¸şéŤéć¨Ąĺź?bool iLodging_HEU::IsHighSpeedMode()
{
    // ĺşćĽçśćä¸éĺşŚĺ¤ć­ĺ¤ąćďźç´ćĽčżĺfalse
    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("ĺşćĽçśćďźéĺşŚĺ¤ć­ĺ¤ąćďźĺźşĺśčżĺééŤéć¨Ąĺź\n");
        return false;
    }

    // ćŻčžDESIRED_SPEEDĺéç˝ŽçéŤéĺ?    bool is_high_speed = (m_dDesiredSpeed >= m_dHighSpeed);

    if (is_high_speed)
    {
        MOOSTrace("ćŁćľĺ°éŤéć¨ĄĺźďźććéĺşŚ=%.1f >= éŤééĺ?%.1f\n",
                  m_dDesiredSpeed, m_dHighSpeed);
    }
    return is_high_speed;
}
