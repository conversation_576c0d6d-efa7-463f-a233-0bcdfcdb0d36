/************************************************************/
/*    NAME: zhaoqinchao                                  */
/*    ORGN: HEU                                            */
/*    FILE: iElevator_HEU.cpp                              */
/*    DESC: ????????                                */
/*          ??????CAN????                          */
/*    DATE: 2024                                           */
/*    VERSION: 1.2.0                                       */
/*                                                          */
/*    MODIFICATION HISTORY:                                 */
/*    v1.2.0 (2024/12/XX) - zhaoqinchao                     */
/*      - ??MotorError?????????????????? */
/*        DRV????????                               */
/*      - ??BlueSocket????????????????     */
/*      - ?????????????????????         */
/*      - ?????????????????????         */
/*      - ???????????????????             */
/*      - ??????????????????              */
/************************************************************/

#include <iterator>
#include <cmath>
#include "MBUtils.h"
#include "iLodging_HEU.h"
using namespace std;
#include <sys/socket.h>
#include <netinet/in.h>
#include <netinet/ip.h> /* superset of previous */

//---------------------------------------------------------
// ???? - ???????

iLodging_HEU::iLodging_HEU()
{
    m_nIterations = 0;
    m_dTimewarp = 1;
    SetAppFreq(10);   // ??????10Hz
    SetCommsFreq(10); // ????10Hz

    // ??????????????????????????GF??
    m_bManualAscend = false;
    m_bManualDescend = false;
    m_bAutoControl = false;
    m_bGFMode = false;
    m_dTargetAngle = 0.0;

    // ???????
    m_dDesiredSpeed = 0.0;

    // ??????????????
    m_dHighSpeed = 2500.0;

    // ??????????????
    m_dCurrentThreshold = 2.0; // ??2.0A

    // ??????????????????- ????????0�-90�??
    m_dMaxAscendAngle = 90.0;   // ?????????????0�-90�
    m_dMinDescendAngle = 0.0;   // ?????????????0�-90�

    // ???????????
    m_dZeroOffset = 0.0;        // ?????????????

    // ????????
    // ????m_dMaxAscendAngle = 3240.0 (9????) - ???
    // ????m_dMinDescendAngle = 360.0 (1????) - ???

    // ?????????
    m_sMainframeState = "";

    // ????????????
    m_bNavigationStarted = false;
    m_sMissionStatus = "";

    // ????????0.7??????0
    m_dCurrentDepth = 0.7;
    m_dDesiredDepth = 1.0;

    // ?????????????????????????????
    m_bAtTop = false;
    m_bAtBottom = false;
    m_bInMotion = false;
    m_bStuckInMiddle = false;

    // ???????
    m_RecvThread = 0;
    m_bThreadRunning = false;

    // ?????????
    // ?????60?
    m_dStartupDelay = 60.0;
    // ????
    m_dStartupTime = 0.0;
    // ?????????
    m_bStartupDelayExecuted = false;

    // ?????????????????????????????????????????????
    m_bHasDescendedAfterNav = false;
    m_bHasAscendedAfterFinish = false;
    m_bHasAscendedInEmergency = false;
    // ???????
    m_sLastMissionStatus = "";

    // ???GF????????
    m_bAscendInProgress = false;
    m_dAscendStartTime = 0.0;
    m_dAscendTimeout = 60.0; // 60???

    m_dMotorTotalAngle = 0.0;
    m_uMotorRounds = 0;
    m_uMotorDegrees = 0;
    m_uMotorCurrent = 0;
    m_uMotorState = STATE_IDLE; // ????????IDLE
    m_uMotorError = ERROR_NONE; // ?????????????
    // ???CAN??????
    m_bCanTimingEnabled = false;

    // ???????????
    m_bDescendCommandSent = false;
    m_dDescendCommandTime = 0.0;
    m_uLastRounds = 0;
    m_bStuckDetected = false;
    m_bStuckDetectionCompleted = false;

    // ???60??????????????
    m_bAutoAscentStuckDetection = false;
    m_dAutoAscentStartTime = 0.0;
    m_uAutoAscentLastRounds = 0;
    m_bAutoAscentStuckHandling = false;
    m_bWaitingForTargetPosition = false;
    m_bWaitingForZeroComplete = false;
}

//---------------------------------------------------------
// ???? - ???????????
iLodging_HEU::~iLodging_HEU()
{
    // ??????
    m_bThreadRunning = false;
    // ??????????
    if (m_RecvThread != 0)
    {
        // ?????????
        fflush(stdout);
        fflush(stderr);

        // ?????????????
        pthread_detach(m_RecvThread);
        m_RecvThread = 0;
    }
        // ??????????????????
    m_RecvSock.Close();
    m_SendSock.Close();
}

//---------------------------------------------------------
// ??MOOS??
bool iLodging_HEU::OnNewMail(MOOSMSG_LIST &NewMail)
{
    MOOSMSG_LIST::iterator p;
    for (p = NewMail.begin(); p != NewMail.end(); p++)
    {
        CMOOSMsg &msg = *p;
        string key = msg.GetKey();
        // CONTROL_MSG??????

        if (key == "CONTROL_MSG")
        {
            // ?????????
            string control_msg = msg.GetString();
            // ??TypeChoice????????
            TypeChoice(control_msg);
        }
        else if (key == "DESIRED_SPEED")
        {
            // ??????
            double old_speed = m_dDesiredSpeed;
            m_dDesiredSpeed = msg.GetDouble();

            // ???????????????????????????
            if (old_speed != m_dDesiredSpeed && !m_bAutoControl && !m_bGFMode)
            {
                m_bAutoControl = true;
            }
        }
        else if (key == "Mainframestate")
        {
            // ??????
            m_sMainframeState = msg.GetString();
            // ???????????
            if (m_sMainframeState == "The master has died")
            {
                m_bManualAscend = true;
                m_bManualDescend = false;
            }
        }
        else if (key == "CtrlMission_STATUS")
        {
            // ??????
            m_sMissionStatus = msg.GetString();
        }
        else if (key == "Depth")
        {
            // ??????
            m_dCurrentDepth = msg.GetDouble();
        }
        else if (key == "DESIRED_DEPTH")
        {
            // ??????
            m_dDesiredDepth = msg.GetDouble();
        }
    }

    return true;
}

//---------------------------------------------------------
// ???MOOS??????
bool iLodging_HEU::OnConnectToServer()
{
    RegisterVariables();
    return (true);
}

//---------------------------------------------------------
// ??????????????
bool iLodging_HEU::Iterate()
{
    m_nIterations++;

    // 1. ??60????????????CAN??????
    static bool s_bAutoStartupExecuted = false; // ?????????????????
    // 50-60???????
    if (!s_bAutoStartupExecuted && m_bCanTimingEnabled && m_dStartupTime > 0)
    {
        // ??????
        double currentTime = MOOSTime();
        // ???????
        double elapsedTime = currentTime - m_dStartupTime;
        // ??????
        if (elapsedTime >= 50 && elapsedTime < 60)
        {
            MOOSTrace("?????50-60??????????=%.1f?\n", elapsedTime);
            // 50s-60s??????????
            if (!m_bStuckDetectionCompleted)
            {
                if (!m_bDescendCommandSent)
                {
                    // ??????????????????
                    m_bManualDescend = true;
                    m_bManualAscend = false;
                    m_bDescendCommandSent = true;
                    m_dDescendCommandTime = currentTime;
                    m_uLastRounds = m_uMotorRounds; // ??????
                    m_bStuckDetected = false;
                }
                else if (!m_bStuckDetected)
                {
                    // ??????????????3??????
                    double detectionTime = currentTime - m_dDescendCommandTime;
                    if (detectionTime <= 3.0)
                    {
                        // ???????????????????
                        double current_amperes = m_uMotorCurrent / 100.0;
                        bool current_exceeded = (current_amperes > m_dCurrentThreshold);

                        // ???????????????
                        bool position_unchanged = (m_uMotorRounds == m_uLastRounds);

                        // ?????????????
                        bool not_at_bottom = !m_bAtBottom;



                        // ????????????(?????????)
                        if (not_at_bottom && (current_exceeded || position_unchanged))
                        {
                            // ????????????
                            SendHoldPositionCommand(m_uMotorRounds, m_uMotorDegrees, 0x02);

                            // ??????
                            SendZeroCommand();

                            // ????????????
                            m_bStuckDetected = true;
                            m_bStuckDetectionCompleted = true;
                        }
                        else
                        {
                            // ???????????????
                            m_uLastRounds = m_uMotorRounds;
                        }
                    }
                    else
                    {
                        m_bStuckDetected = true;           // ?????????????
                        m_bStuckDetectionCompleted = true; // ??????????

                        // ??????????????????60???????????
                        m_bStartupDelayExecuted = false;
                    }
                }
            }
        }
        // ???????????????????????????????
        if (elapsedTime >= m_dStartupDelay)
        {
            // 60????????????GF???????
            if (!m_bGFMode && !IsHighSpeedMode())
            {
                // ???????
                m_bAutoControl = true;
                m_bGFMode = false;
                // ??????????true?????????false?????????true
                m_bManualAscend = true;
                m_bManualDescend = false;
                s_bAutoStartupExecuted = true;  // ?????????
                m_bStartupDelayExecuted = true; // ??????

                // ??60??????????
                m_bAutoAscentStuckDetection = true;
                m_dAutoAscentStartTime = currentTime;
                m_uAutoAscentLastRounds = m_uMotorRounds;
                m_bAutoAscentStuckHandling = false;
            }
            else if (m_bGFMode)
            {
                s_bAutoStartupExecuted = true;  // ?????????
                m_bStartupDelayExecuted = true; // ??????
            }
            else if (IsHighSpeedMode())
            {
                s_bAutoStartupExecuted = true;  // ?????????
                m_bStartupDelayExecuted = true; // ??????
            }
        }
    }

    // 2. 60???????????????
    if (m_bAutoAscentStuckDetection && !m_bAutoAscentStuckHandling)
    {
        double currentTime = MOOSTime();
        double detectionTime = currentTime - m_dAutoAscentStartTime;

        // ???????????????????3??
        if (detectionTime <= 3.0 && !m_bAtTop)
        {
            // ???????????????????
            double current_amperes = m_uMotorCurrent / 100.0;
            bool current_exceeded = (current_amperes > m_dCurrentThreshold);

            // ???????????????
            bool position_unchanged = (m_uMotorRounds == m_uAutoAscentLastRounds);

            // ????????????(?????????)
            if (!m_bAtTop && (current_exceeded || position_unchanged))
            {

                // ??????????? - ??????????????
                double current_angle = ConvertComponentsToAngle(m_uMotorRounds, m_uMotorDegrees);
                double target_angle = current_angle - m_dMaxAscendAngle;

                // ???????????????
                if (target_angle < m_dMinDescendAngle)
                {
                    target_angle = m_dMinDescendAngle;
                }

                AngleComponents target_components = ConvertAngleToComponents(target_angle);

                // ??????????????????
                SendPositionCommand(target_components.rounds, target_components.degrees, 0x02);

                // ???????????????????
                // double target_angle = m_dMinDescendAngle;
                // SendPositionCommand(0, (uint16_t)target_angle, 0x02);

                // ??????
                m_bAutoAscentStuckHandling = true;
                m_bWaitingForTargetPosition = true;
                m_bAutoAscentStuckDetection = false; // ????
            }
            else
            {
                // ????????
                m_uAutoAscentLastRounds = m_uMotorRounds;
            }
        }
        else if (detectionTime > 3.0)
        {
            m_bAutoAscentStuckDetection = false; // ????
        }
        else if (m_bAtTop)
        {
            m_bAutoAscentStuckDetection = false; // ????
        }
    }

    // 3. ?????????
    // ??????????true??????????
    if (m_bManualAscend)
    {
        MOOSTrace("????????\n");
        ExecuteAscendControl();
        m_bManualAscend = false; // ????????
    }

    // ??????????true??????????
    if (m_bManualDescend)
    {
        MOOSTrace("????????\n");
        ExecuteDescendControl();
        m_bManualDescend = false; // ????????

        // ?????????????????60?????????
        if (!m_bStartupDelayExecuted)
        {
            m_bStartupDelayExecuted = true;
            MOOSTrace("???????????????????60?????\n");
        }
    }

    // 3. ???????????????
    if (m_bAutoControl)
    {
        ExecuteAutoControl();
    }

    // 4. ??GF?????
    // ??GF??????true????GF??
    if (m_bGFMode)
    {
        ExecuteGFMode();
    }

    // ?????LIFT???????MOTION > TOP > BOTTOM > MIDDLE?
    std::string lift_status;
    if (m_bInMotion)
    {
        lift_status = "MOTION";
    }
    else if (m_bAtTop)
    {
        lift_status = "TOP";
    }
    else if (m_bAtBottom)
    {
        lift_status = "BOTTOM";
    }
    else if (m_bStuckInMiddle)
    {
        lift_status = "MIDDLE";
    }
    else
    {
        lift_status = " "; // ????
    }

    Notify("LIFT_Status", lift_status);

    // ??????????
    Notify("MOTOR_TOTAL_ANGLE", m_dMotorTotalAngle);
    Notify("MOTOR_ROUNDS", (double)m_uMotorRounds);
    Notify("MOTOR_DEGREES", (double)m_uMotorDegrees);
    Notify("MOTOR_CURRENT", (double)m_uMotorCurrent);
    Notify("MOTOR_STATE", (double)m_uMotorState);
    Notify("MOTOR_ERROR", (double)m_uMotorError);

    // ?Iterate???GF??????????????????????????
    if (m_bGFMode && m_bAscendInProgress && m_bAtTop)
    {
        // GF??????????????????????
        MOOSTrace("GF????????????????GF????????????\n");
        m_bGFMode = false;
        m_bAutoControl = true;
    }

    return true;
}

//---------------------------------------------------------
// ????????????
bool iLodging_HEU::OnStartUp()
{
    // ???????????
    string sRecvIP;
    if (!m_MissionReader.GetConfigurationParam("RecvIP", sRecvIP))
    {
        MOOSTrace("???RecvIP????????0.0.0.0\n");
        sRecvIP = "0.0.0.0";
    }

    int iRecvPort;
    if (!m_MissionReader.GetConfigurationParam("RecvPort", iRecvPort))
    {
        MOOSTrace("??: ????????RecvPort\n");
    }

    string sDestIP;
    if (!m_MissionReader.GetConfigurationParam("DestIP", sDestIP))
    {
        MOOSTrace("??: ??????IP??DestIP\n");
    }

    int iDestPort;
    if (!m_MissionReader.GetConfigurationParam("DestPort", iDestPort))
    {
        MOOSTrace("??: ????????DestPort\n");
    }

    // ??????????????????
    m_MissionReader.GetConfigurationParam("HIGH_SPEED", m_dHighSpeed);

    // ??????????????????
    m_MissionReader.GetConfigurationParam("CURRENT_THRESHOLD", m_dCurrentThreshold);

    // ??????????????????????????????
    m_MissionReader.GetConfigurationParam("MAX_ASCEND_ANGLE", m_dMaxAscendAngle);
    m_MissionReader.GetConfigurationParam("MIN_DESCEND_ANGLE", m_dMinDescendAngle);

    // ??????????
    m_MissionReader.GetConfigurationParam("ZERO_OFFSET", m_dZeroOffset);

    // ????????????????
    m_MissionReader.GetConfigurationParam("STARTUP_DELAY", m_dStartupDelay);

    // ????????????????
    int maxRetries = 5;
    int retryDelay = 2;
    m_MissionReader.GetConfigurationParam("MaxRetries", maxRetries);
    m_MissionReader.GetConfigurationParam("RetryDelay", retryDelay);

    // ?????????????????????????????
    m_dStartupTime = MOOSTime();

    MOOSTrace("????: ??=%.1fmm/s\n", m_dHighSpeed);
    MOOSTrace("????: ??????=%.1fA\n", m_dCurrentThreshold);

    // ??????????????????????
    AngleComponents max_components = ConvertAngleToComponents(m_dMaxAscendAngle);
    AngleComponents min_components = ConvertAngleToComponents(m_dMinDescendAngle);
    MOOSTrace("????: ????=%.1f?(%d?+%d?), ????=%.1f?(%d?+%d?)\n",
              m_dMaxAscendAngle, max_components.rounds, max_components.degrees,
              m_dMinDescendAngle, min_components.rounds, min_components.degrees);

    // ?????????????????
    // MOOSTrace("????????: ????=%.1f�, ????=%.1f�?????0�-90�?\n",
    //           m_dMaxAscendAngle, m_dMinDescendAngle);
    MOOSTrace("??????: %.1f??????\n", m_dStartupDelay);

    // ????? - ??????
    if (!m_RecvSock.OpenSocketWithRetry(sRecvIP, iRecvPort, maxRetries, retryDelay))
    {
        // ??????????MOOS???????????
        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=0;LEVEL=FATAL;NOTE=MOOS File Read Failed");
        MOOSTrace("??: ?????????????%d?????????\n", maxRetries);
    }

    // ??????????????
    if (!m_RecvSock.BindSocket())
    {
        MOOSTrace("???????????...\n");
        if (!m_RecvSock.RebindSocket(maxRetries, retryDelay))
        {
            // ???????????????????????
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=1;LEVEL=FATAL;NOTE=Network Binding Failed");
            MOOSTrace("??: ?????????????%d?????????\n", maxRetries);
            m_RecvSock.Close(); // ?????????
        }
    }

    // ???????1????????
    if (!m_RecvSock.SetReceiveTimeout(1, 0))
    {
        MOOSTrace("??: ???????????\n");
    }

    if (!m_SendSock.OpenSocketWithRetry(sDestIP, iDestPort, maxRetries, retryDelay))
    {
        // ???????????????????
        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=3;LEVEL=FATAL;NOTE=Network Communication Timeout");
        MOOSTrace("??: ?????????????%d?????????\n", maxRetries);
        m_RecvSock.Close(); // ???????????
    }

    // ??????
    m_bThreadRunning = true;
    if (pthread_create(&m_RecvThread, NULL, RecvThreadWrapper, this) != 0)
    {
        // ?????????????????????
        Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=2;LEVEL=FATAL;NOTE=Thread Creation Failed");
        MOOSTrace("??: ????????????????\n");
        m_bThreadRunning = false;
        // ?????????
        m_RecvSock.Close();
        m_SendSock.Close();
    }
    MOOSTrace("????????\n");

    m_dTimewarp = GetMOOSTimeWarp();
    RegisterVariables();

    return true;
}

//---------------------------------------------------------
// ??MOOS??
void iLodging_HEU::RegisterVariables()
{
    // ???????????????????
    Register("CONTROL_MSG", 0);
    Register("DESIRED_SPEED", 0);
    Register("Mainframestate", 0);

    // ????????????????
    Register("CtrlMission_STATUS", 0);

    // ????????????????????????
    Register("Depth", 0);
    Register("DESIRED_DEPTH", 0);
}

//---------------------------------------------------------
// Procedure: RecvThreadWrapper - ??????
void *iLodging_HEU::RecvThreadWrapper(void *arg)
{
    iLodging_HEU *pThis = static_cast<iLodging_HEU *>(arg);
    pThis->RecvFrame();
    return NULL;
}

//---------------------------------------------------------
// Procedure: RecvFrame - ????????
void iLodging_HEU::RecvFrame()
{
    MOOSTrace("???????????recvfrom??IO?\n");

    while (m_bThreadRunning)
    {
        // ??BlueSocket?RecvBinary??????
        vector<uint8_t> Frame;
        int n = m_RecvSock.RecvBinary(Frame, FRAME_LEN);

        if (n <= 0)
        {
            // ??????????
            if (!m_bThreadRunning)
            {
                MOOSTrace("???????????\n");
                break;
            }

            // ???????????????
            continue;
        }

        // ?????????????
        ParseFrame(Frame);

        // ??????????
        if (!m_bThreadRunning)
        {
            break;
        }
    }

    MOOSTrace("??????\n");
}

//---------------------------------------------------------
// Procedure: ParseFrame - ?????????
void iLodging_HEU::ParseFrame(vector<uint8_t> Frame)
{
    // ????????13??????
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("?????: %d\n", Frame.size());
        return;
    }
    // ??13????CAN??????????CAN ID??????????????????????????????
    uint8_t frame_header = Frame[0]; // ?? 0x08
    uint32_t can_id = (Frame[1] << 24) | (Frame[2] << 16) |
                      (Frame[3] << 8) | Frame[4]; // CAN ID (32?)

    // ??????????ID(0x112)???
    if (can_id == 0x112)
    {
        // ?????CAN???????
        if (!m_bCanTimingEnabled)
        {
            m_bCanTimingEnabled = true;
            MOOSTrace("?????CAN ID 0x112?????MOOSTime??\n");
        }

        uint8_t control_mode = Frame[5];                         // ????
        uint8_t motion_state = Frame[6];                         // ????
        uint16_t current_current = (Frame[7] << 8) | Frame[8];   // ???????2-3???????Frame??7-8?
        uint8_t current_rounds = Frame[9];                       // ????
        uint16_t current_degrees = (Frame[10] << 8) | Frame[11]; // ????
        uint8_t error_state = Frame[12];                         // ????

        // ????????????????????
        double total_angle = ConvertComponentsToAngle(current_rounds, current_degrees);

        // ???????????????????????????0�-90�?
        // double current_angle = (double)current_degrees;

        // ??????
        m_dMotorTotalAngle = total_angle;
        m_uMotorRounds = current_rounds;
        m_uMotorDegrees = current_degrees;
        m_uMotorCurrent = current_current;  // ???????
        m_uMotorState = motion_state;
        m_uMotorError = error_state;

        // ????????????
        ProcessMotorError(error_state);

        // ???????
        UpdateElevatorStatus(motion_state, current_rounds, current_degrees, current_current, error_state);

        // ????????????????
        if (error_state == ERROR_STALL)
        {
            // ???GF???????????????
            if (m_bGFMode && m_bAscendInProgress)
            {
                MOOSTrace("?????GF??????????????\n");
                m_bManualDescend = true;
                m_bManualAscend = false;
                m_bAscendInProgress = false;
                MOOSTrace("GF????????????????????????????????\n");
            }
            // ?????????????????
            else if (motion_state != STATE_IDLE)
            {
                MOOSTrace("???????????????????????\n");
                SendHoldPositionCommand(current_rounds, current_degrees, 0x02); // ??????ID??
            }
        }

        // GF????????/????
        if (m_bGFMode && m_bAscendInProgress)
        {
            // ????????????????????
            AngleComponents target = ConvertAngleToComponents(m_dMaxAscendAngle);

            // ?????????????????????5????
            bool ascend_success = (current_rounds == target.rounds) &&
                                  (abs((int)current_degrees - (int)target.degrees) <= 5);

            // ?????????????????
            // bool ascend_success = (abs((double)current_degrees - m_dMaxAscendAngle) <= 5.0);

            if (ascend_success)
            {
                MOOSTrace("GF????????????(%d?+%d?)??????(%d?+%d?)\n",
                          current_rounds, current_degrees, target.rounds, target.degrees);
                // ????????????????
                // MOOSTrace("GF??????????????%.1f�??????%.1f�\n",
                //           (double)current_degrees, m_dMaxAscendAngle);
                MOOSTrace("GF?????GF????????????\n");

                // ???????GF?????????
                m_bGFMode = false;
                m_bAutoControl = true;
                m_bAscendInProgress = false;
                m_bManualAscend = false;
                m_bManualDescend = false;
            }
            else if (error_state != ERROR_NONE || m_bStuckInMiddle)
            {
                // ????????
                if (error_state == ERROR_STALL)
                {
                    MOOSTrace("GF???????????????????\n");
                }
                else
                {
                    MOOSTrace("GF?????????????(???:%d)?????\n", error_state);
                }

                MOOSTrace("GF????????????GF??\n");

                // ??????????????GF??
                m_bManualDescend = true;
                m_bManualAscend = false;
                m_bAscendInProgress = false;
                // m_bGFMode ??? true
            }
        }

        // MOTOR????????Iterate??????????

        MOOSTrace("?????(CAN_ID=0x112): ??=0x%02X, ??=%d, ??=%d, ??=%d, ??=%d, ???=%.1f�, ??=%d\n",
                  control_mode, motion_state, current_current, current_rounds, current_degrees, total_angle, error_state);
    }
    else
    {
        MOOSTrace("?????ID????: CAN_ID=0x%08X?????\n", can_id);
    }
}

//---------------------------------------------------------
// Procedure: UpdateElevatorStatus - ????????
void iLodging_HEU::UpdateElevatorStatus(uint8_t motion_state, uint8_t rounds, uint16_t degrees, uint16_t current, uint8_t error)
{
    // ???????????????
    double current_total_angle = ConvertComponentsToAngle(rounds, degrees);

    // ?????????????????
    AngleComponents max_target = ConvertAngleToComponents(m_dMaxAscendAngle);
    AngleComponents min_target = ConvertAngleToComponents(m_dMinDescendAngle);

    // ?????????????????
    double max_total_angle = ConvertComponentsToAngle(max_target.rounds, max_target.degrees);
    m_bAtTop = (current_total_angle >= (max_total_angle - 10.0)); // 10???

    // ??????????????????
    // double current_angle = (double)degrees;
    // m_bAtTop = (current_angle >= (m_dMaxAscendAngle - 5.0));

    // ?????????????????
    double min_total_angle = ConvertComponentsToAngle(min_target.rounds, min_target.degrees);
    m_bAtBottom = (current_total_angle <= min_total_angle); // ?????????

    // ??????????????????
    // m_bAtBottom = (current_angle <= (m_dMinDescendAngle + 5.0));
    // ????????????0??????????
    m_bInMotion = (current > 0 || motion_state != STATE_IDLE); // STATE_IDLE=2=????

    // ???????????????????????
    m_bStuckInMiddle = (error != ERROR_NONE && !m_bAtTop && !m_bAtBottom);

    // ????????
    if (m_bAtTop && motion_state != STATE_IDLE)
    {
        // ????????????ID (0x02) ????????
        // SendHoldPositionCommand(rounds, degrees, 0x02);

        // ?????????????????false
        m_bInMotion = false;
    }
    else if (m_bAtBottom && motion_state != STATE_IDLE)
    {
        // ????????????ID (0x02) ????????
        SendHoldPositionCommand(rounds, degrees, 0x02);

        // ?????????????????????false
        m_bInMotion = false;
    }
    // ????????
    if (m_bAutoAscentStuckHandling)
    {
        if (m_bWaitingForTargetPosition && motion_state == STATE_IDLE)
        {
            MOOSTrace("??????????????????\n");
            SendZeroCommand();
            m_bWaitingForTargetPosition = false;
            m_bWaitingForZeroComplete = true;
        }
        else if (m_bWaitingForZeroComplete && rounds == 0 && degrees == 0)
        {
            MOOSTrace("??????????????????\n");
            // ????????
            m_bManualAscend = true;
            m_bManualDescend = false;
            m_bAutoControl = true;

            // ????????
            m_bAutoAscentStuckHandling = false;
            m_bWaitingForZeroComplete = false;

            // ????????
            m_bAutoAscentStuckDetection = true;
            m_dAutoAscentStartTime = MOOSTime();
            m_uAutoAscentLastRounds = rounds;

            MOOSTrace("????????????????????\n");
        }
    }
}

//---------------------------------------------------------
// Procedure: ProcessMotorError - ????????????
void iLodging_HEU::ProcessMotorError(uint8_t can_error_code)
{
    // ????????????????????????
    static uint8_t last_error_code = 0xFF;

    // ????????????????
    if (can_error_code != last_error_code)
    {
        switch (can_error_code)
        {
        case 0: // ???
            if (last_error_code != 0xFF && last_error_code != 0)
            {
                MOOSTrace("????????????%d???????\n", last_error_code);
            }
            break;

        case 1: // ??
            // ???????????????????????CAN?????1?
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=4;LEVEL=FATAL;NOTE=Motor Overheating");
            MOOSTrace("???????? (CAN???:1)\n");
            break;

        case 2: // ??
            // ???????????????????????CAN?????2?
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=5;LEVEL=FATAL;NOTE=Motor Overcurrent");
            MOOSTrace("???????? (CAN???:2)\n");
            break;

        case 3: // ????
            // ???????????????????????CAN?????3?
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=6;LEVEL=WARN;NOTE=Motor Undervoltage");
            MOOSTrace("???????? (CAN???:3)\n");
            break;

        case 4: // ?????
            // ????????????????????????CAN?????4?
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=7;LEVEL=FATAL;NOTE=Encoder Error");
            MOOSTrace("????????? (CAN???:4)\n");
            break;

        case 5: // ??
            // ???????????????????????CAN?????5?
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=8;LEVEL=FATAL;NOTE=Motor Stall Detected");
            MOOSTrace("???????? (CAN???:5)\n");
            break;

        case 6: // ??????
            // ???????????????????????CAN?????6?
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=9;LEVEL=WARN;NOTE=Brake Voltage Error");
            MOOSTrace("?????????? (CAN???:6)\n");
            break;

        case 7: // DRV????
            // ????????????????????????CAN?????7?
            Notify("AUV_WARN_MSG", "ID=MOTOR;CODE=10;LEVEL=FATAL;NOTE=DRV Driver Error");
            MOOSTrace("????????? (CAN???:7)\n");
            break;

        default: // ?????
            MOOSTrace("?????????: %d\n", can_error_code);
            break;
        }

        // ???????
        last_error_code = can_error_code;
    }
}

//---------------------------------------------------------
// Procedure: SendFrame - ?????
void iLodging_HEU::SendFrame(vector<uint8_t> Frame)
{
    int result = m_SendSock.SendBinary(Frame);
    if (result < 0)
    {
        MOOSTrace("??????\n");
    }
    else
    {
        MOOSTrace("?????????: %d\n", Frame.size());
    }
}

//---------------------------------------------------------
// Procedure: TypeChoice - ????????
bool iLodging_HEU::TypeChoice(std::string param)
{
    // ????????????:
    // ????: MsgType=control;Act=function;Type=motion;Mode=Ascend/Descend;
    // ????: MsgType=control;Act=function;Type=Autorise;Enable=yes/no;
    // GF??: MsgType=control;Act=function;Type=GF;Enable=yes/no;
    // ????(GF??): MsgType=control;Act=function;Type=stop;Enable=no;
    // ????: Type=function;Act=NavStart;
    // ??????: Type=motion;Act=Start;
    // ????: MsgType=control;ACT=function;TYPE=motion;Mode=MotorSetAngleMode;
    // ???????MsgType=control;ACT=function;TYPE=motion;Reset=yes;

    MOOSTrace("=== TypeChoice ?????????? ===\n");
    MOOSTrace("????: [%s]\n", param.c_str());

    // ????
    std::string sStr = param;
    // ?????
    MOOSToUpper(sStr);
    // ??????????
    MOOSTrimWhiteSpace(sStr);

    // ????
    std::string sOneParam;
    // ?????????
    std::string sParamName;
    // ????????
    std::string sParamVal;

    // ??????????????????MsgType???
    // ??????
    sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is first param
    // ?????=
    sParamName = MOOSChomp(sOneParam, "=");
    // ????
    sParamVal = sOneParam;

    // ??????????? (Type=xxx;Act=xxx)
    if (sParamName == "TYPE")
    {
        std::string sTypeVal = sParamVal;

        // ??Act??
        sOneParam = MOOSChomp(sStr, ";"); // chomp the next ";" ,result is Act
        sParamName = MOOSChomp(sOneParam, "=");
        std::string sActVal = sOneParam;

        if (sParamName == "ACT")
        {
            // ???????? (Type=motion;Act=Start) - ?????????
            if (sTypeVal == "MOTION" && sActVal == "START")
            {
                MOOSTrace("????????? (Type=motion;Act=Start)\n");
                // ????????
                m_bNavigationStarted = true;
                MOOSTrace("?????\n");

                // ????????????ExecuteAutoControl???????
                // ?????GF?????ExecuteGFMode???????
                MOOSTrace("??????????????????\n");
                MOOSTrace("=== TypeChoice ???? ===\n");
                return true;
            }
        }
        else if (sParamName == "ID" && sTypeVal == "CONFIG")
        {
            // ?????? (Type=CONFIG;ID=MOTOR_POS;VALUE=xxx)
            std::string sIdVal = sOneParam;

            // ??VALUE??
            sOneParam = MOOSChomp(sStr, ";");
            sParamName = MOOSChomp(sOneParam, "=");
            std::string sValueVal = sOneParam;

            if (sParamName == "VALUE" && sIdVal == "MOTOR_POS")
            {
                MOOSTrace("???????????\n");
                double target_angle = atof(sValueVal.c_str());
                MOOSTrace("??????: %.1f?\n", target_angle);

                // ???????????
                m_dTargetAngle = target_angle;
                m_bManualAscend = true;
                m_bManualDescend = false;

                MOOSTrace("=== TypeChoice ???? ===\n");
                return true;
            }
            else
            {
                MOOSTrace("???CONFIG???????ID=%s?VALUE??=%s\n", sIdVal.c_str(), sParamName.c_str());
                return false;
            }
        }

        MOOSTrace("?????????????: Type=%s;Act=%s\n", sTypeVal.c_str(), sActVal.c_str());
        return false;
    }

    // ??????MSGTYPE????????
    if (sParamName != "MSGTYPE")
    {
        MOOSTrace("?????????????????MSGTYPE?TYPE\n");
        return false;
    }

    // ???control
    if (sParamVal == "CONTROL")
    {
        // ??????ACT
        // ??????
        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is ACT
        // ?????=
        sParamName = MOOSChomp(sOneParam, "=");
        // ???
        sParamVal = sOneParam;

        // ??????????ACT
        if (sParamName == "ACT")
        {
            // ??Act??
            std::string sActVal = sParamVal;

            if (sParamVal == "FUNCTION")
            {
                // ??????TYPE
                sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is TYPE
                sParamName = MOOSChomp(sOneParam, "=");
                std::string sTypeVal = sOneParam;

                if (sParamName == "TYPE")
                {
                    // ??TYPE?????????
                    if (sTypeVal == "MOTION")
                    {
                        // ??????Mode??
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Mode
                        std::string sModeParam = MOOSChomp(sOneParam, "=");
                        std::string sModeVal = sOneParam;

                        MOOSTrace("???motion?????? - ???=%s, ???=%s\n", sModeParam.c_str(), sModeVal.c_str());

                        if (sModeParam == "MODE")
                        {
                            if (sModeVal == "ASCEND")
                            {
                                MOOSTrace("??????????\n");
                                m_bManualAscend = true;
                                m_bManualDescend = false; // ???????false

                                // ?????????????????GF??
                                if (m_bAutoControl || m_bGFMode)
                                {
                                    MOOSTrace("???????????????????\n");
                                    m_bAutoControl = false;
                                    m_bGFMode = false;
                                }

                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else if (sModeVal == "DESCEND")
                            {
                                MOOSTrace("??????????\n");
                                m_bManualDescend = true;
                                m_bManualAscend = false; // ???????false

                                // ?????????????????GF??
                                if (m_bAutoControl || m_bGFMode)
                                {
                                    MOOSTrace("???????????????????\n");
                                    m_bAutoControl = false;
                                    m_bGFMode = false;
                                }

                                // ???????????????????60?????????
                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bStartupDelayExecuted = true;
                                    MOOSTrace("?????????????????????60?????\n");
                                }

                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else if (sModeVal == "MOTORSETANGLEMODE")
                            {
                                MOOSTrace("??????????? - ????????????\n");
                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("??????Mode??: %s\n", sModeVal.c_str());
                            }
                        }
                        else if (sModeParam == "RESET")
                        {
                            if (sModeVal == "YES")
                            {
                                MOOSTrace("???????????????\n");
                                // ????????
                                SendZeroCommand();
                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("???Reset????YES????: %s\n", sModeVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("???motion????Mode?Reset??????: %s\n", sModeParam.c_str());
                        }
                    }
                    else if (sTypeVal == "AUTORISE")
                    {
                        // ??????Enable??
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "YES")
                            {
                                MOOSTrace("?????????????\n");
                                m_bAutoControl = true;
                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else if (sEnableVal == "NO")
                            {
                                MOOSTrace("?????????????\n");
                                m_bAutoControl = false;
                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("???Enable????YES?NO????: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("???Autorise????Enable??\n");
                        }
                    }
                    else if (sTypeVal == "GF")
                    {
                        // ??????Enable??
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "YES")
                            {
                                MOOSTrace("??GF?????60?????\n");
                                m_bGFMode = true;
                                // ???????????????
                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bManualAscend = false;
                                    m_bManualDescend = false;
                                    MOOSTrace("GF?????????\n");
                                }
                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else if (sEnableVal == "NO")
                            {
                                MOOSTrace("??GF??\n");
                                m_bGFMode = false;
                                // ??????????????? ?? (???????????)
                                bool can_ascend = false;

                                // ??1???????????????????CAN???????
                                if (!m_bStartupDelayExecuted && m_bCanTimingEnabled && m_dStartupTime > 0)
                                {
                                    double currentTime = MOOSTime();
                                    double elapsedTime = currentTime - m_dStartupTime;
                                    if (elapsedTime >= m_dStartupDelay)
                                    {
                                        can_ascend = true;
                                        MOOSTrace("GF????????????????CAN???????\n");
                                    }
                                }

                                // ??2???????????????=0
                                if ((m_sMissionStatus == "FINISH" || m_sMissionStatus == "EMERGENCY") &&
                                    m_dDesiredSpeed == 0.0)
                                {
                                    if (m_sMissionStatus == "FINISH")
                                    {
                                        // ???????????????<0.5???????
                                        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
                                        {
                                            can_ascend = true;
                                            MOOSTrace("GF??????????????=0?????\n");
                                        }
                                    }
                                    else if (m_sMissionStatus == "EMERGENCY")
                                    {
                                        // ?????????
                                        can_ascend = true;
                                        MOOSTrace("GF??????????????=0\n");
                                    }
                                }

                                if (can_ascend)
                                {
                                    MOOSTrace("??????\n");
                                    m_bManualAscend = true;
                                    m_bManualDescend = false;
                                    m_bStartupDelayExecuted = true;
                                }

                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("???Enable????YES?NO????: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("???GF????Enable??\n");
                        }
                    }
                    else if (sTypeVal == "STOP")
                    {
                        // ??????Enable??
                        sOneParam = MOOSChomp(sStr, ";"); // chomp the first ";" ,result is Enable
                        std::string sEnableParam = MOOSChomp(sOneParam, "=");
                        std::string sEnableVal = sOneParam;

                        if (sEnableParam == "ENABLE")
                        {
                            if (sEnableVal == "NO")
                            {
                                MOOSTrace("???????????GF??\n");
                                // ??GF?????60?????
                                m_bGFMode = true;
                                // ???????????????
                                if (!m_bStartupDelayExecuted)
                                {
                                    m_bManualAscend = false;
                                    m_bManualDescend = false;
                                    MOOSTrace("GF?????????\n");
                                }
                                MOOSTrace("=== TypeChoice ???? ===\n");
                                return true;
                            }
                            else
                            {
                                MOOSTrace("???STOP???Enable????NO????: %s\n", sEnableVal.c_str());
                            }
                        }
                        else
                        {
                            MOOSTrace("???STOP????Enable??\n");
                        }
                    }
                    else
                    {
                        MOOSTrace("??????Type??: %s\n", sTypeVal.c_str());
                    }
                }
                else
                {
                    MOOSTrace("??????????TYPE\n");
                }
            }
            else
            {
                MOOSTrace("??????????ACT=FUNCTION\n");
            }
        }
        else
        {
            MOOSTrace("???MsgType??CONTROL\n");
        }

        MOOSTrace("=== TypeChoice ???????????? ===\n");
        return false;
    }

    // ????false?????????
    MOOSTrace("=== TypeChoice ????????? ===\n");
    return false;
}
//---------------------------------------------------------
// Procedure: ExecuteAscendControl - ????????????
void iLodging_HEU::ExecuteAscendControl()
{
    MOOSTrace("??????????\n");
    // ????????? (13????CAN??)
    vector<uint8_t> ascendFrame(FRAME_LEN, 0);

    // CAN???? (??0-4) - ????CAN ID = 0x02
    ascendFrame[0] = 0x08; // ????
    ascendFrame[1] = 0x00; // CAN ID???? (0x02 >> 24)
    ascendFrame[2] = 0x00; // CAN ID??? (0x02 >> 16)
    ascendFrame[3] = 0x00; // CAN ID????? (0x02 >> 8)
    ascendFrame[4] = 0x02; // CAN ID????? (0x02 & 0xFF)

    // CAN???? (??5-12) - ????????
    // ??5: ???? BB=??????
    ascendFrame[5] = 0xBB;

    // ??6: ????
    ascendFrame[6] = 0x00;

    // ??7-8: ?? (???????0)
    MOOSTrace("?????????????0\n");
    ascendFrame[7] = 0x00; // ????
    ascendFrame[8] = 0x00; // ????

    // ??9-10: ???? - ??90?????
    // 90????????0x7FFF
    ascendFrame[9] = 0x7F; // ??????
    ascendFrame[10] = 0xFF; // ??????

    // ??11-12: ????
    ascendFrame[11] = 0x00;
    ascendFrame[12] = 0x00;

    SendFrame(ascendFrame);
    MOOSTrace("??????????(CAN ID=0x02): ??=0x%02X, ????=90�, ???=0x7FFF\n",
              ascendFrame[5]);
}

//---------------------------------------------------------
// Procedure: ExecuteDescendControl - ????????????
void iLodging_HEU::ExecuteDescendControl()
{
    MOOSTrace("??????????\n");

    // ????????? (13????CAN??)
    vector<uint8_t> descendFrame(FRAME_LEN, 0);

    // CAN???? (??0-4) - ????CAN ID = 0x02
    descendFrame[0] = 0x08; // ????
    descendFrame[1] = 0x00; // CAN ID???? (0x02 >> 24)
    descendFrame[2] = 0x00; // CAN ID??? (0x02 >> 16)
    descendFrame[3] = 0x00; // CAN ID????? (0x02 >> 8)
    descendFrame[4] = 0x02; // CAN ID????? (0x02 & 0xFF)

    // CAN???? (??5-12) - ????????
    // ??5: ???? BB=??????
    descendFrame[5] = 0xBB;

    // ??6: ????
    descendFrame[6] = 0x00;

    // ??7-8: ?? (???????0)
    MOOSTrace("?????????????0\n");
    descendFrame[7] = 0x00; // ????
    descendFrame[8] = 0x00; // ????

    // ??9-10: ???? - ??0?????
    // 0????????0x4000
    descendFrame[9] = 0x40; // ??????
    descendFrame[10] = 0x00; // ??????

    // ??11-12: ????
    descendFrame[11] = 0x00;
    descendFrame[12] = 0x00;

    SendFrame(descendFrame);
    MOOSTrace("??????????(CAN ID=0x02): ??=0x%02X, ????=0�, ???=0x4000\n",
              descendFrame[5]);
}

//---------------------------------------------------------
// Procedure: ExecuteAutoControl - ??????
void iLodging_HEU::ExecuteAutoControl()
{
    // ???????????????????????

    // 1. ??????????????
    if (m_bNavigationStarted && !m_bHasDescendedAfterNav)
    {
        MOOSTrace("???????????????\n");
        m_bManualDescend = true;
        m_bManualAscend = false;
        m_bHasDescendedAfterNav = true;

        // ???????????????????????????
        if (!m_bStartupDelayExecuted)
        {
            m_bStartupDelayExecuted = true;
            MOOSTrace("???????????????????????????\n");
        }
    }

    // 2. ?????????CtrlMission_STATUS = "FINISH" ??????GF????????
    MOOSTrace("???????????=%s, GF??=%s, ????=%s\n",
              m_sMissionStatus.c_str(), m_bGFMode ? "?" : "?", IsHighSpeedMode() ? "?" : "?");

    if (m_sMissionStatus == "FINISH" && !m_bGFMode && !IsHighSpeedMode())
    {
        MOOSTrace("???????????????\n");

        // ?????????????????0??????
        MOOSTrace("??????????? - ????=%.1f\n", m_dDesiredSpeed);
        // ??????????0
        if (m_dDesiredSpeed == 0.0)
        {
            MOOSTrace("??????????????????? - ???=%s, ????=%s, ????=%s\n",
                      m_bHasAscendedAfterFinish ? "?" : "?",
                      m_bManualAscend ? "?" : "?",
                      m_bManualDescend ? "?" : "?");

            // ?? m_bHasAscendedAfterFinish ???????FINISH?????
            if (!m_bManualAscend && !m_bManualDescend)
            {
                MOOSTrace("?????????=%.1f???????????????\n", m_dDesiredSpeed);
                m_bManualAscend = true;
                m_bManualDescend = false;
                // ????????????????????
                // m_bHasAscendedAfterFinish = true;
            }
            else
            {
                MOOSTrace("??????????????????\n");
            }
        }
        else
        {
            MOOSTrace("??????????????\n");
        }
    }
    else
    {
        MOOSTrace("???????????????????\n");
    }
    if (m_sMissionStatus == "FINISH" && IsHighSpeedMode())
    {
        MOOSTrace("?????????????????????\n");
    }

    // 4. ???????????????????
    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("?????????????????=%.1f\n", m_dDesiredSpeed);

        // ????????????EMERGENCY??????
        if (m_sLastMissionStatus != "EMERGENCY")
        {
            m_bHasAscendedInEmergency = false;
            MOOSTrace("???????????????\n");
        }

        if (!m_bHasAscendedInEmergency && !m_bManualAscend && !m_bManualDescend)
        {
            MOOSTrace("??????????????????????\n");
            m_bManualAscend = true;
            m_bManualDescend = false;
            m_bHasAscendedInEmergency = true;
        }
        else
        {
            MOOSTrace("????????=%s, ????=%s, ????=%s\n",
                      m_bHasAscendedInEmergency ? "?" : "?",
                      m_bManualAscend ? "?" : "?",
                      m_bManualDescend ? "?" : "?");
        }
    }

    // 5. ????????????DESIRED_SPEED????
    // ????????????????
    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("??????????????????\n");
    }
    else
    {
        // 60?????????????????/??
        MOOSTrace("????????? - ????=%.0f mm/s, ????=%.0f mm/s\n",
                  m_dDesiredSpeed, m_dHighSpeed);

        // ????????????????????????
        if (!m_bManualAscend && !m_bManualDescend)
    {
        // ???????????????
        if (m_dDesiredSpeed < m_dHighSpeed && !m_bAtTop)
        {
            MOOSTrace("???????(%.0f < %.0f)?????\n",
                      m_dDesiredSpeed, m_dHighSpeed);
            m_bManualAscend = true;
            m_bManualDescend = false;
        }
        // ????????????
        else if (m_dDesiredSpeed >= m_dHighSpeed && !m_bAtBottom)
        {
            MOOSTrace("???????(%.0f >= %.0f)?????\n",
                      m_dDesiredSpeed, m_dHighSpeed);
            m_bManualDescend = true;
            m_bManualAscend = false;
        }
        else
        {
            MOOSTrace("??????????????????\n");
        }
    }
        else
        {
            MOOSTrace("?????????????????????\n");
        }
    }

    // ????????
    m_sLastMissionStatus = m_sMissionStatus;
}

//---------------------------------------------------------
// Procedure: ExecuteGFMode - ??GF??
void iLodging_HEU::ExecuteGFMode()
{
    // ???????????????0.5??60s???????
    // ??GF??????????????????????????GF??
    MOOSTrace("??GF????\n");

    // GF????????????????????????????

    // 1. ??????????
    if (m_bManualAscend)
    {
        MOOSTrace("GF???????????\n");
        m_bManualAscend = false; // ??????
    }

    // 2. GF?????????????????????????????
    // ????????????
    bool should_ascend_now = false;

    // ??????????
    if (m_sMissionStatus == "FINISH" && m_dDesiredSpeed == 0.0 && !IsHighSpeedMode())
    {
        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
        {
            should_ascend_now = true;
        }
    }

    // ??????????????????
    if (m_sMissionStatus == "EMERGENCY")
    {
        should_ascend_now = true;
        MOOSTrace("GF??????????????????????\n");
    }

    // ????????????????
    if (!m_bAtBottom && !m_bManualDescend && !should_ascend_now)
    {
        MOOSTrace("GF?????GF??????????????????\n");
        m_bManualDescend = true; // ??????
        m_bManualAscend = false; // ???????false
    }
    else if (should_ascend_now)
    {
        MOOSTrace("GF?????????????????\n");
    }

    // 3. ????????GF???????
    bool should_exit_gf_and_ascend = false;

    // ??1??????????????????????
    if (m_sMissionStatus == "FINISH" && m_dDesiredSpeed == 0.0 && !IsHighSpeedMode())
    {
        MOOSTrace("GF????????? - ????=FINISH, ????=%.1f, ????=%s\n",
                  m_dDesiredSpeed, IsHighSpeedMode() ? "?" : "?");

        // ???????????????<0.5???????
        MOOSTrace("GF????????? - ????=%s, ????=%.2f, ????=%.2f\n",
                  m_bNavigationStarted ? "?" : "?", m_dCurrentDepth, m_dDesiredDepth);

        if (m_bNavigationStarted && m_dCurrentDepth < 0.5 && m_dDesiredDepth == 0.0)
        {
            should_exit_gf_and_ascend = true;
            MOOSTrace("GF??????????????\n");
        }
        else
        {
            MOOSTrace("GF?????????????\n");
        }
    }
    else if (m_sMissionStatus == "FINISH" && IsHighSpeedMode())
    {
        MOOSTrace("GF??????????????????????\n");
    }

    // ??4????? - ????????????
    if (m_sMissionStatus == "EMERGENCY")
    {
        // ?????????????????????
        MOOSTrace("GF??????????????????????=%.1f?\n", m_dDesiredSpeed);
        should_exit_gf_and_ascend = true; // ????????
    }

    // ??5???????????
    if (m_bNavigationStarted && m_sMissionStatus != "FINISH")
    {
        // ?????????????????
        MOOSTrace("GF????????????????????\n");
        should_exit_gf_and_ascend = false; // ????
    }

    // ??????GF????????
    MOOSTrace("GF???should_exit_gf_and_ascend = %s\n", should_exit_gf_and_ascend ? "true" : "false");
    if (should_exit_gf_and_ascend)
    {
        // ???????????????????????????
        if (!m_bAscendInProgress)
        {
            MOOSTrace("GF????????????????????????\n");

            // ????GF??????????
            m_bGFMode = false;
            m_bAutoControl = true;

            // ??????
            m_bManualAscend = true;     // ??????
            m_bManualDescend = false;   // ??????
            m_bAscendInProgress = true; // ????????

            // ?????????????????
            if (!m_bStartupDelayExecuted)
            {
                m_bStartupDelayExecuted = true;
            }

            MOOSTrace("GF??????GF????????????\n");
        }
        else
        {
            MOOSTrace("GF?????????????m_bAscendInProgress = true\n");
        }
        // ?????????????????????ParseFrame???
    }
    else
    {
        MOOSTrace("GF?????????????????????\n");
    }

    MOOSTrace("GF????????\n");
}

//---------------------------------------------------------
// Procedure: SendHoldPositionCommand - ????????
void iLodging_HEU::SendHoldPositionCommand(uint8_t current_rounds, uint16_t current_degrees, uint8_t motor_id)
{
    // ?????????????????????????
    vector<uint8_t> holdFrame(FRAME_LEN, 0);

    // CAN???? (??0-4) - ???????CAN ID
    holdFrame[0] = 0x08;     // ????
    holdFrame[1] = 0x00;     // CAN ID???? (motor_id >> 24)
    holdFrame[2] = 0x00;     // CAN ID??? (motor_id >> 16)
    holdFrame[3] = 0x00;     // CAN ID????? (motor_id >> 8)
    holdFrame[4] = motor_id; // CAN ID????? (motor_id & 0xFF)

    // CAN???? (??5-12) - ??????
    holdFrame[5] = 0xBB;                           // ???? = ????
    holdFrame[6] = 0x00;                           // ????
    holdFrame[7] = 0x00;                           // ???? (???????0)
    holdFrame[8] = 0x00;                           // ???? (???????0)

    // ?????????
    uint16_t fall_value = ConvertAngleToFallValue((double)current_degrees);

    holdFrame[9] = (fall_value >> 8) & 0xFF;       // ??????
    holdFrame[10] = fall_value & 0xFF;             // ??????
    holdFrame[11] = 0x00;                          // ????
    holdFrame[12] = 0x00;                          // ????

    SendFrame(holdFrame);
    MOOSTrace("????????????(CAN ID=0x%02X): ??=0x%02X, ????=%.1f�, ???=0x%04X\n",
              motor_id, holdFrame[5], (double)current_degrees, fall_value);
}

//---------------------------------------------------------
// Procedure: SendZeroCommand - ??????
void iLodging_HEU::SendZeroCommand()
{
    // ??????????CAN ID 0x7FF
    vector<uint8_t> zeroFrame(FRAME_LEN, 0);

    // CAN???? (??0-4) - ????CAN ID = 0x7FF
    zeroFrame[0] = 0x08; // ????
    zeroFrame[1] = 0x00; // CAN ID???? (0x7FF >> 24)
    zeroFrame[2] = 0x00; // CAN ID??? (0x7FF >> 16)
    zeroFrame[3] = 0x07; // CAN ID????? (0x7FF >> 8)
    zeroFrame[4] = 0xFF; // CAN ID????? (0x7FF & 0xFF)

    // CAN???? (??5-12) - ????????????
    zeroFrame[5] = 0x00;  // ??ID?
    zeroFrame[6] = 0x01;  // ?????
    zeroFrame[7] = 0x00;  // ????
    zeroFrame[8] = 0x03;  // ?????
    zeroFrame[9] = 0x00;  // ????
    zeroFrame[10] = 0x00; // ????
    zeroFrame[11] = 0x00; // ????
    zeroFrame[12] = 0x00; // ????

    SendFrame(zeroFrame);
    MOOSTrace("???????(CAN ID=0x7FF): ??ID=0x%02X, ???=0x%02X, ??=0x%02X\n",
              zeroFrame[5], zeroFrame[6], zeroFrame[8]);
}

//---------------------------------------------------------
// Procedure: SendPositionCommand - ??????????????
void iLodging_HEU::SendPositionCommand(uint8_t target_rounds, uint16_t target_degrees, uint8_t motor_id)
{
    // ???????????
    vector<uint8_t> positionFrame(FRAME_LEN, 0);

    // CAN???? (??0-4) - ???????ID
    positionFrame[0] = 0x08;     // ????
    positionFrame[1] = 0x00;     // CAN ID????
    positionFrame[2] = 0x00;     // CAN ID???
    positionFrame[3] = 0x00;     // CAN ID?????
    positionFrame[4] = motor_id; // CAN ID?????

    // CAN???? (??5-12) - ??????????
    positionFrame[5] = 0xBB;                          // ??????
    positionFrame[6] = 0x00;                          // ????
    positionFrame[7] = 0x00;                          // ????????????0?
    positionFrame[8] = 0x00;                          // ????

    // ?????????????????????????
    // target_rounds??????????????target_degrees
    uint16_t fall_value = ConvertAngleToFallValue((double)target_degrees);

    positionFrame[9] = (fall_value >> 8) & 0xFF;      // ??????
    positionFrame[10] = fall_value & 0xFF;            // ??????
    positionFrame[11] = 0x00;                         // ????
    positionFrame[12] = 0x00;                         // ????

    SendFrame(positionFrame);
    MOOSTrace("???????????(CAN ID=0x%02X): ??=0x%02X, ????=%.1f�, ???=0x%04X\n",
              motor_id, positionFrame[5], (double)target_degrees, fall_value);
}

/*
//---------------------------------------------------------
// Procedure: ConvertAngleToComponents - ?????????+????
// ????????????????????????????0�-90�
// ???????????????????????
AngleComponents iElevator_HEU::ConvertAngleToComponents(double total_angle)
{
    AngleComponents result;

    // ???????
    if (total_angle < 0)
    {
        total_angle = 0;
        MOOSTrace("????????????0\n");
    }

    // ??????
    result.rounds = (uint8_t)(total_angle / 360.0);

    // ??????
    double remaining_degrees = total_angle - (result.rounds * 360.0);
    result.degrees = (uint16_t)remaining_degrees;

    // ?????0-359???
    if (result.degrees >= 360)
    {
        result.rounds += result.degrees / 360;
        result.degrees = result.degrees % 360;
    }
    // ???????????????????
    MOOSTrace("????: %.1f� -> %d? + %d?\n",
              total_angle, result.rounds, result.degrees);

    return result;
}
*/

//---------------------------------------------------------
// Procedure: ConvertAngleToFallValue - ???????????
uint16_t iLodging_HEU::ConvertAngleToFallValue(double angle)
{
    // ??????????????-10�?100�
    if (angle < -10.0)
    {
        angle = -10.0;
        MOOSTrace("??????????-10�????-10�\n");
    }
    else if (angle > 100.0)
    {
        angle = 100.0;
        MOOSTrace("??????????100�????100�\n");
    }

    // ???????0�??0x7FFF?90�??0x4000??????
    // ????-10�?100�???
    // -10�????0x7FFF?????0x7FFF?
    // 100�????0x4000?????0x4000?

    // ??????0-90?????
    double normalized_ratio;
    if (angle < 0.0) {
        // ????0�??0x7FFF?-10�???0x7FFF????
        normalized_ratio = 0.0;
    } else if (angle > 90.0) {
        // ??90�?90�??0x4000?100�???0x4000????
        normalized_ratio = 1.0;
    } else {
        // 0�-90�????????
        normalized_ratio = angle / 90.0;
    }

    uint16_t fall_value = 0x7FFF - (uint16_t)(normalized_ratio * (0x7FFF - 0x4000));

    MOOSTrace("??????: %.1f� -> 0x%04X\n", angle, fall_value);

    return fall_value;
}

//---------------------------------------------------------
// Procedure: ConvertFallValueToAngle - ???????????
double iLodging_HEU::ConvertFallValueToAngle(uint16_t fall_value)
{
    // ??????????????
    if (fall_value < 0x4000)
    {
        fall_value = 0x4000;
        MOOSTrace("??????????0x4000????0x4000???100�?\n");
    }
    else if (fall_value > 0x7FFF)
    {
        fall_value = 0x7FFF;
        MOOSTrace("??????????0x7FFF????0x7FFF???-10�?\n");
    }

    // ???????0�??0x7FFF?90�??0x4000??????
    // ?????angle = ((0x7FFF - fall_value) / (0x7FFF - 0x4000)) * 90.0
    double angle = ((double)(0x7FFF - fall_value) / (double)(0x7FFF - 0x4000)) * 90.0;

    MOOSTrace("???????: 0x%04X -> %.1f�\n", fall_value, angle);

    return angle;
}

//---------------------------------------------------------
// Procedure: ConvertComponentsToAngle - ???+????????
// ????????????????????????????0�-90�
// ????????????????????????
double iLodging_HEU::ConvertComponentsToAngle(uint8_t rounds, uint16_t degrees)
{
    // ??????????
    if (degrees >= 360)
    {
        MOOSTrace("??????%d??359?????\n", degrees);
        // ?????????????
        // rounds += degrees/360 ? ??????
        // degrees = degrees%360 ? ??????
        rounds += degrees / 360;
        degrees = degrees % 360;
    }
    // ???????+??
    double total_angle = rounds * 360.0 + degrees;

    MOOSTrace("????: %d? + %d? -> %.1f�\n",
              rounds, degrees, total_angle);

    return total_angle;
}

//---------------------------------------------------------
// Procedure: IsHighSpeedMode - ?????????
bool iLodging_HEU::IsHighSpeedMode()
{
    // ????????????????false
    if (m_sMissionStatus == "EMERGENCY")
    {
        MOOSTrace("?????????????????????\n");
        return false;
    }

    // ??DESIRED_SPEED???????
    bool is_high_speed = (m_dDesiredSpeed >= m_dHighSpeed);

    if (is_high_speed)
    {
        MOOSTrace("????????????=%.1f >= ????=%.1f\n",
                  m_dDesiredSpeed, m_dHighSpeed);
    }
    return is_high_speed;
}
