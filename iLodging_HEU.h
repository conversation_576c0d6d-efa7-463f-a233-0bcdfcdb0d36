/************************************************************/
/*    NAME: zhaoqinchao                                  */
/*    ORGN: HEU                                             */
/*    FILE: iLodging_HEU.h                                 */
/*    DESC: ????????????????0°-90°?          */
/*    DATE: 2024                                            */
/************************************************************/
#ifndef iLodging_HEU_HEADER
#define iLodging_HEU_HEADER
#include "MOOS/libMOOS/MOOSLib.h"
#include "MBUtils.h"
#include "BlueSocket.h"
#include <string>
#include <vector>
#include <fstream>

const size_t FRAME_LEN = 13;

// ??????????+?????
// ??????????0°-90°??????????
// ???????????????????rounds??0
struct AngleComponents
{
    uint8_t rounds;   // ?? (???????0)
    uint16_t degrees; // ?? (?????0-90)
};

// ????????
enum ControlMode
{
    MODE_ANGLE = 0xBB // ??????
};

// ????????
enum MotorState
{
    STATE_RISING = 0x00,  // ????
    STATE_FALLING = 0x01, // ????
    STATE_IDLE = 0x02     // ????
};

// ????????
enum MotorError
{
    ERROR_NONE = 0x00,          // ???
    ERROR_OVERHEAT = 0x01,      // ??
    ERROR_OVERCURRENT = 0x02,   // ??
    ERROR_UNDERVOLTAGE = 0x03,  // ????
    ERROR_ENCODER = 0x04,       // ?????
    ERROR_STALL = 0x05,         // ??
    ERROR_BRAKE_VOLTAGE = 0x06, // ??????
    ERROR_DRV_DRIVER = 0x07     // DRV????
};

class iLodging_HEU : public CMOOSApp
{
public:
    iLodging_HEU();
    ~iLodging_HEU();
    bool OnNewMail(MOOSMSG_LIST &NewMail);
    bool Iterate();
    bool OnConnectToServer();
    bool OnStartUp();
    void RegisterVariables();

private:
    // ????
    void RecvFrame();
    // ??????
    void ParseFrame(std::vector<uint8_t> Frame);
    // ??????
    void SendFrame(std::vector<uint8_t> Frame);
    // ?????????????????????????????????????????
    void UpdateElevatorStatus(uint8_t motion_state, uint8_t rounds, uint16_t degrees, uint16_t current, uint8_t error);
    // ???????????????
    void ProcessMotorError(uint8_t can_error_code);
    // ??????
    void ExecuteAscendControl();
    // ??????
    void ExecuteDescendControl();
    // ??????
    void ExecuteAutoControl();
    // GF????
    void ExecuteGFMode();
    // ??????????
    void SendHoldPositionCommand(uint8_t current_rounds, uint16_t current_degrees, uint8_t motor_id);
    // ????????
    void SendZeroCommand();
    // ??????????
    void SendPositionCommand(uint8_t target_rounds, uint16_t target_degrees, uint8_t motor_id);
    // ??????
    bool IsHighSpeedMode();
    // ??????
    AngleComponents ConvertAngleToComponents(double total_angle);
    // ?????? - ?????????????????????
    double ConvertComponentsToAngle(uint8_t rounds, uint16_t degrees);
    // ????????????(0°-90°)????????(0°?0x7FFF, 90°?0x4000)
    uint16_t ConvertAngleToFallValue(double angle);
    // ?????????????????????(0x7FFF?0°, 0x4000?90°)
    double ConvertFallValueToAngle(uint16_t fall_value);
    // ??????
    bool TypeChoice(std::string param);

    // ??????
    class BlueSocket m_RecvSock;
    class BlueSocket m_SendSock;

    // ????
    pthread_t m_RecvThread;
    bool m_bThreadRunning;
    static void *RecvThreadWrapper(void *arg);

private: // ????
    unsigned int m_nIterations;
    double m_dTimewarp;

    // ?????
    bool m_bManualAscend;  // ??????
    bool m_bManualDescend; // ??????
    bool m_bAutoControl;   // ??????
    bool m_bGFMode;        // GF???????60??????
    double m_dTargetAngle; // ????

    // ????
    double m_dDesiredSpeed;

    // ??????
    double m_dHighSpeed; // ????

    // ??????
    double m_dCurrentThreshold; // ????????????

    // ??????
    double m_dMaxAscendAngle;  // ??????
    double m_dMinDescendAngle; // ??????

    // ??????????10????
    double m_dZeroOffset;      // ????????-10.0 ? +10.0 ??

    // ????
    std::string m_sMainframeState;

    // ???????
    bool m_bNavigationStarted;    // ???????
    std::string m_sMissionStatus; // ????

    // ??????
    double m_dCurrentDepth; // ????
    double m_dDesiredDepth; // ????

    // ???????
    bool m_bAtTop;         // ??????
    bool m_bAtBottom;      // ??????
    bool m_bInMotion;      // ??????
    bool m_bStuckInMiddle; // ???????

    // ??????
    double m_dStartupDelay;       // ??????60s???
    double m_dStartupTime;        // ??????
    bool m_bStartupDelayExecuted; // ?????????

    // ????????????????
    bool m_bHasDescendedAfterNav;     // ??????????
    bool m_bHasAscendedAfterFinish;   // ??????????
    bool m_bHasAscendedInEmergency;   // ?????????
    std::string m_sLastMissionStatus; // ???????

    // GF????????
    bool m_bAscendInProgress;  // ?????????
    double m_dAscendStartTime; // ??????
    double m_dAscendTimeout;   // ?????????

    double m_dMotorTotalAngle; // ?????
    uint8_t m_uMotorRounds;    // ????
    uint16_t m_uMotorDegrees;  // ????
    uint16_t m_uMotorCurrent;  // ????
    uint8_t m_uMotorState;     // ????
    uint8_t m_uMotorError;     // ?????

    // ????????
    bool m_bDescendCommandSent;      // ????????
    double m_dDescendCommandTime;    // ????????
    uint8_t m_uLastRounds;           // ???????
    bool m_bStuckDetected;           // ??????
    bool m_bStuckDetectionCompleted; // ????????

    // 60??????????????
    bool m_bAutoAscentStuckDetection; // ??????????
    double m_dAutoAscentStartTime;    // ????????
    uint8_t m_uAutoAscentLastRounds;  // ???????????
    bool m_bAutoAscentStuckHandling;  // ??????????
    bool m_bWaitingForTargetPosition; // ????????
    bool m_bWaitingForZeroComplete;   // ??????
    // ??CAN???????
    bool m_bCanTimingEnabled; // CAN????????
};

#endif