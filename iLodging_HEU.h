/************************************************************/
/*    NAME: zhaoqinchao                                  */
/*    ORGN: HEU                                             */
/*    FILE: iElevator_HEU.h                                 */
/*    DESC: 倒伏电机上位机通信系统（倒伏协议0°-90°）          */
/*    DATE: 2024                                            */
/************************************************************/
#ifndef iElevator_HEU_HEADER
#define iElevator_HEU_HEADER
#include "MOOS/libMOOS/MOOSLib.h"
#include "MBUtils.h"
#include "BlueSocket.h"
#include <string>
#include <vector>
#include <fstream>

const size_t FRAME_LEN = 13;

// 角度组件结构体（圈数+度数格式）
// 注意：倒伏协议仅支持0°-90°角度范围，无圈数概念
// 此结构体主要用于兼容性，实际倒伏控制中rounds应为0
struct AngleComponents
{
    uint8_t rounds;   // 圈数 (倒伏协议中应为0)
    uint16_t degrees; // 度数 (倒伏协议：0-90)
};

// 电机控制模式定义
enum ControlMode
{
    MODE_ANGLE = 0xBB // 角度控制模式
};

// 电机运动状态定义
enum MotorState
{
    STATE_RISING = 0x00,  // 上升状态
    STATE_FALLING = 0x01, // 下降状态
    STATE_IDLE = 0x02     // 静止状态
};

// 电机错误状态定义
enum MotorError
{
    ERROR_NONE = 0x00,          // 无异常
    ERROR_OVERHEAT = 0x01,      // 过热
    ERROR_OVERCURRENT = 0x02,   // 过流
    ERROR_UNDERVOLTAGE = 0x03,  // 电压过低
    ERROR_ENCODER = 0x04,       // 编码器错误
    ERROR_STALL = 0x05,         // 堵转
    ERROR_BRAKE_VOLTAGE = 0x06, // 刹车电压过高
    ERROR_DRV_DRIVER = 0x07     // DRV驱动错误
};

class iElevator_HEU : public CMOOSApp
{
public:
    iElevator_HEU();
    ~iElevator_HEU();
    bool OnNewMail(MOOSMSG_LIST &NewMail);
    bool Iterate();
    bool OnConnectToServer();
    bool OnStartUp();
    void RegisterVariables();

private:
    // 网络通信
    void RecvFrame();
    // 解析帧的函数
    void ParseFrame(std::vector<uint8_t> Frame);
    // 发送帧的函数
    void SendFrame(std::vector<uint8_t> Frame);
    // 更新升降机状态的函数，这个就是更新那几个标志位的，升到顶，降到底，在运动，卡在中间
    void UpdateElevatorStatus(uint8_t motion_state, uint8_t rounds, uint16_t degrees, uint16_t current, uint8_t error);
    // 处理电机错误码并发送告警的函数
    void ProcessMotorError(uint8_t can_error_code);
    // 控制执行函数
    void ExecuteAscendControl();
    // 下降控制函数
    void ExecuteDescendControl();
    // 自动控制函数
    void ExecuteAutoControl();
    // GF模式函数
    void ExecuteGFMode();
    // 发送保持位置命令函数
    void SendHoldPositionCommand(uint8_t current_rounds, uint16_t current_degrees, uint8_t motor_id);
    // 发送清零指令函数
    void SendZeroCommand();
    // 发送位置控制指令函数
    void SendPositionCommand(uint8_t target_rounds, uint16_t target_degrees, uint8_t motor_id);
    // 高速判断函数
    bool IsHighSpeedMode();
    // 角度转换函数
    AngleComponents ConvertAngleToComponents(double total_angle);
    // 角度转换函数 - 倒伏协议不需要此函数，但保留以防兼容性需要
    double ConvertComponentsToAngle(uint8_t rounds, uint16_t degrees);
    // 倒伏角度转换函数：将角度(0°-90°)转换为倒伏协议值(0°→0x7FFF, 90°→0x4000)
    uint16_t ConvertAngleToFallValue(double angle);
    // 倒伏协议值转换函数：将倒伏协议值转换为角度(0x7FFF→0°, 0x4000→90°)
    double ConvertFallValueToAngle(uint16_t fall_value);
    // 消息解析函数
    bool TypeChoice(std::string param);

    // 网络通信对象
    class BlueSocket m_RecvSock;
    class BlueSocket m_SendSock;

    // 线程相关
    pthread_t m_RecvThread;
    bool m_bThreadRunning;
    static void *RecvThreadWrapper(void *arg);

private: // 状态变量
    unsigned int m_nIterations;
    double m_dTimewarp;

    // 控制标志位
    bool m_bManualAscend;  // 手动上升标志
    bool m_bManualDescend; // 手动下降标志
    bool m_bAutoControl;   // 自动控制标志
    bool m_bGFMode;        // GF模式标志（停止60秒自动升起）
    double m_dTargetAngle; // 目标角度

    // 期望速度
    double m_dDesiredSpeed;

    // 速度配置参数
    double m_dHighSpeed; // 高速模式

    // 电流配置参数
    double m_dCurrentThreshold; // 卡住检测电流阈值（安培）

    // 角度配置参数
    double m_dMaxAscendAngle;  // 最大上升角度
    double m_dMinDescendAngle; // 最小下降角度

    // 倒伏电机微调参数（±10度范围）
    double m_dZeroOffset;      // 零位微调偏移量（-10.0 到 +10.0 度）

    // 主控状态
    std::string m_sMainframeState;

    // 导航和任务状态
    bool m_bNavigationStarted;    // 导航是否已启动
    std::string m_sMissionStatus; // 任务状态

    // 深度相关变量
    double m_dCurrentDepth; // 当前深度
    double m_dDesiredDepth; // 期望深度

    // 四个状态标志位
    bool m_bAtTop;         // 升到顶标志位
    bool m_bAtBottom;      // 降到底标志位
    bool m_bInMotion;      // 在运动标志位
    bool m_bStuckInMiddle; // 卡在中间标志位

    // 启动延迟控制
    double m_dStartupDelay;       // 启动延迟时间60s（秒）
    double m_dStartupTime;        // 程序启动时间
    bool m_bStartupDelayExecuted; // 启动延迟是否已执行

    // 自动控制状态标志（防止重复执行）
    bool m_bHasDescendedAfterNav;     // 导航启动后是否已下沉
    bool m_bHasAscendedAfterFinish;   // 任务结束后是否已上升
    bool m_bHasAscendedInEmergency;   // 应急状态是否已上升
    std::string m_sLastMissionStatus; // 上次的任务状态

    // GF模式升起状态跟踪
    bool m_bAscendInProgress;  // 升起操作进行中标志
    double m_dAscendStartTime; // 升起开始时间
    double m_dAscendTimeout;   // 升起超时时间（秒）

    double m_dMotorTotalAngle; // 电机总角度
    uint8_t m_uMotorRounds;    // 电机圈数
    uint16_t m_uMotorDegrees;  // 电机角度
    uint16_t m_uMotorCurrent;  // 电机电流
    uint8_t m_uMotorState;     // 电机状态
    uint8_t m_uMotorError;     // 电机错误码

    // 卡住检测相关变量
    bool m_bDescendCommandSent;      // 下降指令发送标志
    double m_dDescendCommandTime;    // 下降指令发送时间
    uint8_t m_uLastRounds;           // 上次记录的圈数
    bool m_bStuckDetected;           // 卡住检测标志
    bool m_bStuckDetectionCompleted; // 卡住检测完成标志

    // 60秒后自动上升卡住处理相关变量
    bool m_bAutoAscentStuckDetection; // 自动上升卡住检测标志
    double m_dAutoAscentStartTime;    // 自动上升开始时间
    uint8_t m_uAutoAscentLastRounds;  // 自动上升上次记录的圈数
    bool m_bAutoAscentStuckHandling;  // 正在处理自动上升卡住
    bool m_bWaitingForTargetPosition; // 等待到达目标位置
    bool m_bWaitingForZeroComplete;   // 等待清零完成
    // 基于CAN报文的计时控制
    bool m_bCanTimingEnabled; // CAN报文计时使能标志
};

#endif